/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/(docs)/docs/[[...slug]]/page";
exports.ids = ["app/(docs)/docs/[[...slug]]/page"];
exports.modules = {

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&page=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&appPaths=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&pagePath=private-next-app-dir%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage.tsx&appDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&page=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&appPaths=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&pagePath=private-next-app-dir%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage.tsx&appDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport safe */ _Users_rang_codespace_onlyrules_website_project_packages_web_app_global_error_tsx__WEBPACK_IMPORTED_MODULE_24__[\"default\"]),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/module.compiled.js?87f3\");\n/* harmony import */ var next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/../../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/../../node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/../../node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/../../node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/../../node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/app-render/interop-default */ \"(rsc)/../../node_modules/next/dist/server/app-render/interop-default.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/../../node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/experimental/ppr */ \"(rsc)/../../node_modules/next/dist/server/lib/experimental/ppr.js\");\n/* harmony import */ var next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/request/fallback-params */ \"(rsc)/../../node_modules/next/dist/server/request/fallback-params.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/app-render/encryption-utils */ \"(rsc)/../../node_modules/next/dist/server/app-render/encryption-utils.js\");\n/* harmony import */ var next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__);\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/lib/streaming-metadata */ \"(rsc)/../../node_modules/next/dist/server/lib/streaming-metadata.js\");\n/* harmony import */ var next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/app-render/action-utils */ \"(rsc)/../../node_modules/next/dist/server/app-render/action-utils.js\");\n/* harmony import */ var next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/server/lib/server-action-request-meta */ \"(rsc)/../../node_modules/next/dist/server/lib/server-action-request-meta.js\");\n/* harmony import */ var next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/client/components/app-router-headers */ \"(rsc)/../../node_modules/next/dist/client/components/app-router-headers.js\");\n/* harmony import */ var next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/is-bot */ \"next/dist/shared/lib/router/utils/is-bot\");\n/* harmony import */ var next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/../../node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__);\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! next/dist/lib/fallback */ \"(rsc)/../../node_modules/next/dist/lib/fallback.js\");\n/* harmony import */ var next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__);\n/* harmony import */ var next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! next/dist/server/render-result */ \"(rsc)/../../node_modules/next/dist/server/render-result.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/../../node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__);\n/* harmony import */ var next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! next/dist/server/stream-utils/encoded-tags */ \"(rsc)/../../node_modules/next/dist/server/stream-utils/encoded-tags.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! next/dist/server/send-payload */ \"(rsc)/../../node_modules/next/dist/server/send-payload.js\");\n/* harmony import */ var next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__);\n/* harmony import */ var _Users_rang_codespace_onlyrules_website_project_packages_web_app_global_error_tsx__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./app/global-error.tsx */ \"(rsc)/./app/global-error.tsx\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__);\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! next/dist/client/components/redirect-status-code */ \"(rsc)/../../node_modules/next/dist/client/components/redirect-status-code.js\");\n/* harmony import */ var next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"__next_app__\",\"routeModule\",\"handler\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\nconst module0 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/global-error.tsx */ \"(rsc)/./app/global-error.tsx\"));\nconst module1 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/global-error.tsx */ \"(rsc)/./app/global-error.tsx\"));\nconst module2 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/../../node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module3 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/../../node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module4 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/../../node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst module5 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(docs)/layout.tsx */ \"(rsc)/./app/(docs)/layout.tsx\"));\nconst module6 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/not-found.js */ \"(rsc)/../../node_modules/next/dist/client/components/builtin/not-found.js\", 23));\nconst module7 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/forbidden.js */ \"(rsc)/../../node_modules/next/dist/client/components/builtin/forbidden.js\", 23));\nconst module8 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/builtin/unauthorized.js */ \"(rsc)/../../node_modules/next/dist/client/components/builtin/unauthorized.js\", 23));\nconst page9 = () => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/(docs)/docs/[[...slug]]/page.tsx */ \"(rsc)/./app/(docs)/docs/[[...slug]]/page.tsx\"));\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        '(docs)',\n        {\n        children: [\n        'docs',\n        {\n        children: [\n        '[[...slug]]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [page9, \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [module5, \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\"],\n'not-found': [module6, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module7, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module8, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      },\n        {\n        'global-error': [module0, \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\"],\n'global-error': [module1, \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\"],\n'not-found': [module2, \"next/dist/client/components/builtin/not-found.js\"],\n'forbidden': [module3, \"next/dist/client/components/builtin/forbidden.js\"],\n'unauthorized': [module4, \"next/dist/client/components/builtin/unauthorized.js\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx\"];\n\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/(docs)/docs/[[...slug]]/page\",\n        pathname: \"/docs/[[...slug]]\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: '',\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || ''\n});\nasync function handler(req, res, ctx) {\n    var _this;\n    let srcPage = \"/(docs)/docs/[[...slug]]/page\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const initialPostponed = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'postponed');\n    // TODO: replace with more specific flags\n    const minimalMode = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'minimalMode');\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, query, params, parsedUrl, pageIsDynamic, buildManifest, nextFontManifest, reactLoadableManifest, serverActionsManifest, clientReferenceManifest, subresourceIntegrityManifest, prerenderManifest, isDraftMode, resolvedPathname, revalidateOnlyGenerated, routerServerContext, nextConfig } = prepareResult;\n    const pathname = parsedUrl.pathname || '/';\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_13__.normalizeAppPath)(srcPage);\n    let { isOnDemandRevalidate } = prepareResult;\n    const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n    const isPrerendered = prerenderManifest.routes[resolvedPathname];\n    let isSSG = Boolean(prerenderInfo || isPrerendered || prerenderManifest.routes[normalizedSrcPage]);\n    const userAgent = req.headers['user-agent'] || '';\n    const botType = (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.getBotType)(userAgent);\n    const isHtmlBot = (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.isHtmlBotRequest)(req);\n    /**\n   * If true, this indicates that the request being made is for an app\n   * prefetch request.\n   */ const isPrefetchRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isPrefetchRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_ROUTER_PREFETCH_HEADER]);\n    // NOTE: Don't delete headers[RSC] yet, it still needs to be used in renderToHTML later\n    const isRSCRequest = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'isRSCRequest') ?? Boolean(req.headers[next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.RSC_HEADER]);\n    const isPossibleServerAction = (0,next_dist_server_lib_server_action_request_meta__WEBPACK_IMPORTED_MODULE_14__.getIsPossibleServerAction)(req);\n    /**\n   * If the route being rendered is an app page, and the ppr feature has been\n   * enabled, then the given route _could_ support PPR.\n   */ const couldSupportPPR = (0,next_dist_server_lib_experimental_ppr__WEBPACK_IMPORTED_MODULE_8__.checkIsAppPPREnabled)(nextConfig.experimental.ppr);\n    // When enabled, this will allow the use of the `?__nextppronly` query to\n    // enable debugging of the static shell.\n    const hasDebugStaticShellQuery =  false && 0;\n    // When enabled, this will allow the use of the `?__nextppronly` query\n    // to enable debugging of the fallback shell.\n    const hasDebugFallbackShellQuery = hasDebugStaticShellQuery && query.__nextppronly === 'fallback';\n    // This page supports PPR if it is marked as being `PARTIALLY_STATIC` in the\n    // prerender manifest and this is an app page.\n    const isRoutePPREnabled = couldSupportPPR && (((_this = prerenderManifest.routes[normalizedSrcPage] ?? prerenderManifest.dynamicRoutes[normalizedSrcPage]) == null ? void 0 : _this.renderingMode) === 'PARTIALLY_STATIC' || // Ideally we'd want to check the appConfig to see if this page has PPR\n    // enabled or not, but that would require plumbing the appConfig through\n    // to the server during development. We assume that the page supports it\n    // but only during development.\n    hasDebugStaticShellQuery && (routeModule.isDev === true || (routerServerContext == null ? void 0 : routerServerContext.experimentalTestProxy) === true));\n    const isDebugStaticShell = hasDebugStaticShellQuery && isRoutePPREnabled;\n    // We should enable debugging dynamic accesses when the static shell\n    // debugging has been enabled and we're also in development mode.\n    const isDebugDynamicAccesses = isDebugStaticShell && routeModule.isDev === true;\n    const isDebugFallbackShell = hasDebugFallbackShellQuery && isRoutePPREnabled;\n    // If we're in minimal mode, then try to get the postponed information from\n    // the request metadata. If available, use it for resuming the postponed\n    // render.\n    const minimalPostponed = isRoutePPREnabled ? initialPostponed : undefined;\n    // If PPR is enabled, and this is a RSC request (but not a prefetch), then\n    // we can use this fact to only generate the flight data for the request\n    // because we can't cache the HTML (as it's also dynamic).\n    const isDynamicRSCRequest = isRoutePPREnabled && isRSCRequest && !isPrefetchRSCRequest;\n    // Need to read this before it's stripped by stripFlightHeaders. We don't\n    // need to transfer it to the request meta because it's only read\n    // within this function; the static segment data should have already been\n    // generated, so we will always either return a static response or a 404.\n    const segmentPrefetchHeader = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'segmentPrefetchRSCRequest');\n    // TODO: investigate existing bug with shouldServeStreamingMetadata always\n    // being true for a revalidate due to modifying the base-server this.renderOpts\n    // when fixing this to correct logic it causes hydration issue since we set\n    // serveStreamingMetadata to true during export\n    let serveStreamingMetadata = !userAgent ? true : (0,next_dist_server_lib_streaming_metadata__WEBPACK_IMPORTED_MODULE_11__.shouldServeStreamingMetadata)(userAgent, nextConfig.htmlLimitedBots);\n    if (isHtmlBot && isRoutePPREnabled) {\n        isSSG = false;\n        serveStreamingMetadata = false;\n    }\n    // In development, we always want to generate dynamic HTML.\n    let supportsDynamicResponse = // If we're in development, we always support dynamic HTML, unless it's\n    // a data request, in which case we only produce static HTML.\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isSSG || // If this request has provided postponed data, it supports dynamic\n    // HTML.\n    typeof initialPostponed === 'string' || // If this is a dynamic RSC request, then this render supports dynamic\n    // HTML (it's dynamic).\n    isDynamicRSCRequest;\n    // When html bots request PPR page, perform the full dynamic rendering.\n    const shouldWaitOnAllReady = isHtmlBot && isRoutePPREnabled;\n    let ssgCacheKey = null;\n    if (!isDraftMode && isSSG && !supportsDynamicResponse && !isPossibleServerAction && !minimalPostponed && !isDynamicRSCRequest) {\n        ssgCacheKey = resolvedPathname;\n    }\n    // the staticPathKey differs from ssgCacheKey since\n    // ssgCacheKey is null in dev since we're always in \"dynamic\"\n    // mode in dev to bypass the cache, but we still need to honor\n    // dynamicParams = false in dev mode\n    let staticPathKey = ssgCacheKey;\n    if (!staticPathKey && routeModule.isDev) {\n        staticPathKey = resolvedPathname;\n    }\n    const ComponentMod = {\n        ...next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_25__,\n        tree,\n        pages,\n        GlobalError: _Users_rang_codespace_onlyrules_website_project_packages_web_app_global_error_tsx__WEBPACK_IMPORTED_MODULE_24__[\"default\"],\n        handler,\n        routeModule,\n        __next_app__\n    };\n    // Before rendering (which initializes component tree modules), we have to\n    // set the reference manifests to our global store so Server Action's\n    // encryption util can access to them at the top level of the page module.\n    if (serverActionsManifest && clientReferenceManifest) {\n        (0,next_dist_server_app_render_encryption_utils__WEBPACK_IMPORTED_MODULE_10__.setReferenceManifestsSingleton)({\n            page: srcPage,\n            clientReferenceManifest,\n            serverActionsManifest,\n            serverModuleMap: (0,next_dist_server_app_render_action_utils__WEBPACK_IMPORTED_MODULE_12__.createServerModuleMap)({\n                serverActionsManifest\n            })\n        });\n    }\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    try {\n        const invokeRouteModule = async (span, context)=>{\n            const nextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextRequest(req);\n            const nextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_7__.NodeNextResponse(res);\n            // TODO: adapt for putting the RDC inside the postponed data\n            // If we're in dev, and this isn't a prefetch or a server action,\n            // we should seed the resume data cache.\n            if (true) {\n                if (nextConfig.experimental.dynamicIO && !isPrefetchRSCRequest && !context.renderOpts.isPossibleServerAction) {\n                    const warmup = await routeModule.warmup(nextReq, nextRes, context);\n                    // If the warmup is successful, we should use the resume data\n                    // cache from the warmup.\n                    if (warmup.metadata.renderResumeDataCache) {\n                        context.renderOpts.renderResumeDataCache = warmup.metadata.renderResumeDataCache;\n                    }\n                }\n            }\n            return routeModule.render(nextReq, nextRes, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const doRender = async ({ span, postponed, fallbackRouteParams })=>{\n            const context = {\n                query,\n                params,\n                page: normalizedSrcPage,\n                sharedContext: {\n                    buildId\n                },\n                serverComponentsHmrCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'serverComponentsHmrCache'),\n                fallbackRouteParams,\n                renderOpts: {\n                    App: ()=>null,\n                    Document: ()=>null,\n                    pageConfig: {},\n                    ComponentMod,\n                    Component: (0,next_dist_server_app_render_interop_default__WEBPACK_IMPORTED_MODULE_6__.interopDefault)(ComponentMod),\n                    params,\n                    routeModule,\n                    page: srcPage,\n                    postponed,\n                    shouldWaitOnAllReady,\n                    serveStreamingMetadata,\n                    supportsDynamicResponse: typeof postponed === 'string' || supportsDynamicResponse,\n                    buildManifest,\n                    nextFontManifest,\n                    reactLoadableManifest,\n                    subresourceIntegrityManifest,\n                    serverActionsManifest,\n                    clientReferenceManifest,\n                    setIsrStatus: routerServerContext == null ? void 0 : routerServerContext.setIsrStatus,\n                    dir: routeModule.projectDir,\n                    isDraftMode,\n                    isRevalidate: isSSG && !postponed && !isDynamicRSCRequest,\n                    botType,\n                    isOnDemandRevalidate,\n                    isPossibleServerAction,\n                    assetPrefix: nextConfig.assetPrefix,\n                    nextConfigOutput: nextConfig.output,\n                    crossOrigin: nextConfig.crossOrigin,\n                    trailingSlash: nextConfig.trailingSlash,\n                    previewProps: prerenderManifest.preview,\n                    deploymentId: nextConfig.deploymentId,\n                    enableTainting: nextConfig.experimental.taint,\n                    htmlLimitedBots: nextConfig.htmlLimitedBots,\n                    devtoolSegmentExplorer: nextConfig.experimental.devtoolSegmentExplorer,\n                    reactMaxHeadersLength: nextConfig.reactMaxHeadersLength,\n                    multiZoneDraftMode,\n                    incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'incrementalCache'),\n                    cacheLifeProfiles: nextConfig.experimental.cacheLife,\n                    basePath: nextConfig.basePath,\n                    serverActions: nextConfig.experimental.serverActions,\n                    ...isDebugStaticShell || isDebugDynamicAccesses ? {\n                        nextExport: true,\n                        supportsDynamicResponse: false,\n                        isStaticGeneration: true,\n                        isRevalidate: true,\n                        isDebugDynamicAccesses: isDebugDynamicAccesses\n                    } : {},\n                    experimental: {\n                        isRoutePPREnabled,\n                        expireTime: nextConfig.expireTime,\n                        staleTimes: nextConfig.experimental.staleTimes,\n                        dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                        clientSegmentCache: Boolean(nextConfig.experimental.clientSegmentCache),\n                        dynamicOnHover: Boolean(nextConfig.experimental.dynamicOnHover),\n                        inlineCss: Boolean(nextConfig.experimental.inlineCss),\n                        authInterrupts: Boolean(nextConfig.experimental.authInterrupts),\n                        clientTraceMetadata: nextConfig.experimental.clientTraceMetadata || []\n                    },\n                    waitUntil: ctx.waitUntil,\n                    onClose: (cb)=>{\n                        res.on('close', cb);\n                    },\n                    onAfterTaskError: ()=>{},\n                    onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext),\n                    err: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'invokeError'),\n                    dev: routeModule.isDev\n                }\n            };\n            const result = await invokeRouteModule(span, context);\n            const { metadata } = result;\n            const { cacheControl, headers = {}, // Add any fetch tags that were on the page to the response headers.\n            fetchTags: cacheTags } = metadata;\n            if (cacheTags) {\n                headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n            }\n            // Pull any fetch metrics from the render onto the request.\n            ;\n            req.fetchMetrics = metadata.fetchMetrics;\n            // we don't throw static to dynamic errors in dev as isSSG\n            // is a best guess in dev since we don't have the prerender pass\n            // to know whether the path is actually static or not\n            if (isSSG && (cacheControl == null ? void 0 : cacheControl.revalidate) === 0 && !routeModule.isDev && !isRoutePPREnabled) {\n                const staticBailoutInfo = metadata.staticBailoutInfo;\n                const err = Object.defineProperty(new Error(`Page changed from static to dynamic at runtime ${resolvedPathname}${(staticBailoutInfo == null ? void 0 : staticBailoutInfo.description) ? `, reason: ${staticBailoutInfo.description}` : ``}` + `\\nsee more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E132\",\n                    enumerable: false,\n                    configurable: true\n                });\n                if (staticBailoutInfo == null ? void 0 : staticBailoutInfo.stack) {\n                    const stack = staticBailoutInfo.stack;\n                    err.stack = err.message + stack.substring(stack.indexOf('\\n'));\n                }\n                throw err;\n            }\n            return {\n                value: {\n                    kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE,\n                    html: result,\n                    headers,\n                    rscData: metadata.flightData,\n                    postponed: metadata.postponed,\n                    status: metadata.statusCode,\n                    segmentData: metadata.segmentData\n                },\n                cacheControl\n            };\n        };\n        const responseGenerator = async ({ hasResolved, previousCacheEntry, isRevalidating, span })=>{\n            const isProduction = routeModule.isDev === false;\n            const didRespond = hasResolved || res.writableEnded;\n            // skip on-demand revalidate if cache is not present and\n            // revalidate-if-generated is set\n            if (isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry && !minimalMode) {\n                if (routerServerContext == null ? void 0 : routerServerContext.render404) {\n                    await routerServerContext.render404(req, res);\n                } else {\n                    res.statusCode = 404;\n                    res.end('This page could not be found');\n                }\n                return null;\n            }\n            let fallbackMode;\n            if (prerenderInfo) {\n                fallbackMode = (0,next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.parseFallbackField)(prerenderInfo.fallback);\n            }\n            // When serving a bot request, we want to serve a blocking render and not\n            // the prerendered page. This ensures that the correct content is served\n            // to the bot in the head.\n            if (fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.PRERENDER && (0,next_dist_shared_lib_router_utils_is_bot__WEBPACK_IMPORTED_MODULE_16__.isBot)(userAgent)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if ((previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) === -1) {\n                isOnDemandRevalidate = true;\n            }\n            // TODO: adapt for PPR\n            // only allow on-demand revalidate for fallback: true/blocking\n            // or for prerendered fallback: false paths\n            if (isOnDemandRevalidate && (fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND || previousCacheEntry)) {\n                fallbackMode = next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER;\n            }\n            if (!minimalMode && fallbackMode !== next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.BLOCKING_STATIC_RENDER && staticPathKey && !didRespond && !isDraftMode && pageIsDynamic && (isProduction || !isPrerendered)) {\n                // if the page has dynamicParams: false and this pathname wasn't\n                // prerendered trigger the no fallback handling\n                if (// In development, fall through to render to handle missing\n                // getStaticPaths.\n                (isProduction || prerenderInfo) && // When fallback isn't present, abort this render so we 404\n                fallbackMode === next_dist_lib_fallback__WEBPACK_IMPORTED_MODULE_18__.FallbackMode.NOT_FOUND) {\n                    throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError();\n                }\n                let fallbackResponse;\n                if (isRoutePPREnabled && !isRSCRequest) {\n                    // We use the response cache here to handle the revalidation and\n                    // management of the fallback shell.\n                    fallbackResponse = await routeModule.handleResponse({\n                        cacheKey: isProduction ? normalizedSrcPage : null,\n                        req,\n                        nextConfig,\n                        routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                        isFallback: true,\n                        prerenderManifest,\n                        isRoutePPREnabled,\n                        responseGenerator: async ()=>doRender({\n                                span,\n                                // We pass `undefined` as rendering a fallback isn't resumed\n                                // here.\n                                postponed: undefined,\n                                fallbackRouteParams: // If we're in production or we're debugging the fallback\n                                // shell then we should postpone when dynamic params are\n                                // accessed.\n                                isProduction || isDebugFallbackShell ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(normalizedSrcPage) : null\n                            }),\n                        waitUntil: ctx.waitUntil\n                    });\n                    // If the fallback response was set to null, then we should return null.\n                    if (fallbackResponse === null) return null;\n                    // Otherwise, if we did get a fallback response, we should return it.\n                    if (fallbackResponse) {\n                        // Remove the cache control from the response to prevent it from being\n                        // used in the surrounding cache.\n                        delete fallbackResponse.cacheControl;\n                        return fallbackResponse;\n                    }\n                }\n            }\n            // Only requests that aren't revalidating can be resumed. If we have the\n            // minimal postponed data, then we should resume the render with it.\n            const postponed = !isOnDemandRevalidate && !isRevalidating && minimalPostponed ? minimalPostponed : undefined;\n            // When we're in minimal mode, if we're trying to debug the static shell,\n            // we should just return nothing instead of resuming the dynamic render.\n            if ((isDebugStaticShell || isDebugDynamicAccesses) && typeof postponed !== 'undefined') {\n                return {\n                    cacheControl: {\n                        revalidate: 1,\n                        expire: undefined\n                    },\n                    value: {\n                        kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.PAGES,\n                        html: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                        pageData: {},\n                        headers: undefined,\n                        status: undefined\n                    }\n                };\n            }\n            // If this is a dynamic route with PPR enabled and the default route\n            // matches were set, then we should pass the fallback route params to\n            // the renderer as this is a fallback revalidation request.\n            const fallbackRouteParams = pageIsDynamic && isRoutePPREnabled && ((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'renderFallbackShell') || isDebugFallbackShell) ? (0,next_dist_server_request_fallback_params__WEBPACK_IMPORTED_MODULE_9__.getFallbackRouteParams)(pathname) : null;\n            // Perform the render.\n            return doRender({\n                span,\n                postponed,\n                fallbackRouteParams\n            });\n        };\n        const handleResponse = async (span)=>{\n            var _cacheEntry_value, _cachedData_headers;\n            const cacheEntry = await routeModule.handleResponse({\n                cacheKey: ssgCacheKey,\n                responseGenerator: (c)=>responseGenerator({\n                        span,\n                        ...c\n                    }),\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n                isOnDemandRevalidate,\n                isRoutePPREnabled,\n                req,\n                nextConfig,\n                prerenderManifest,\n                waitUntil: ctx.waitUntil\n            });\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            // In dev, we should not cache pages for any reason.\n            if (routeModule.isDev) {\n                res.setHeader('Cache-Control', 'no-store, must-revalidate');\n            }\n            if (!cacheEntry) {\n                if (ssgCacheKey) {\n                    // A cache entry might not be generated if a response is written\n                    // in `getInitialProps` or `getServerSideProps`, but those shouldn't\n                    // have a cache key. If we do have a cache key but we don't end up\n                    // with a cache entry, then either Next.js or the application has a\n                    // bug that needs fixing.\n                    throw Object.defineProperty(new Error('invariant: cache entry required but not generated'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E62\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                return null;\n            }\n            if (((_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant app-page handler received invalid cache entry ${(_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E707\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            const didPostpone = typeof cacheEntry.value.postponed === 'string';\n            if (isSSG && // We don't want to send a cache header for requests that contain dynamic\n            // data. If this is a Dynamic RSC request or wasn't a Prefetch RSC\n            // request, then we should set the cache header.\n            !isDynamicRSCRequest && (!didPostpone || isPrefetchRSCRequest)) {\n                if (!minimalMode) {\n                    // set x-nextjs-cache header to match the header\n                    // we set for the image-optimizer\n                    res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n                }\n                // Set a header used by the client router to signal the response is static\n                // and should respect the `static` cache staleTime value.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_IS_PRERENDER_HEADER, '1');\n            }\n            const { value: cachedData } = cacheEntry;\n            // Coerce the cache control parameter from the render.\n            let cacheControl;\n            // If this is a resume request in minimal mode it is streamed with dynamic\n            // content and should not be cached.\n            if (minimalPostponed) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (minimalMode && isRSCRequest && !isPrefetchRSCRequest && isRoutePPREnabled) {\n                cacheControl = {\n                    revalidate: 0,\n                    expire: undefined\n                };\n            } else if (!routeModule.isDev) {\n                // If this is a preview mode request, we shouldn't cache it\n                if (isDraftMode) {\n                    cacheControl = {\n                        revalidate: 0,\n                        expire: undefined\n                    };\n                } else if (!isSSG) {\n                    if (!res.getHeader('Cache-Control')) {\n                        cacheControl = {\n                            revalidate: 0,\n                            expire: undefined\n                        };\n                    }\n                } else if (cacheEntry.cacheControl) {\n                    // If the cache entry has a cache control with a revalidate value that's\n                    // a number, use it.\n                    if (typeof cacheEntry.cacheControl.revalidate === 'number') {\n                        var _cacheEntry_cacheControl;\n                        if (cacheEntry.cacheControl.revalidate < 1) {\n                            throw Object.defineProperty(new Error(`Invalid revalidate configuration provided: ${cacheEntry.cacheControl.revalidate} < 1`), \"__NEXT_ERROR_CODE\", {\n                                value: \"E22\",\n                                enumerable: false,\n                                configurable: true\n                            });\n                        }\n                        cacheControl = {\n                            revalidate: cacheEntry.cacheControl.revalidate,\n                            expire: ((_cacheEntry_cacheControl = cacheEntry.cacheControl) == null ? void 0 : _cacheEntry_cacheControl.expire) ?? nextConfig.expireTime\n                        };\n                    } else {\n                        cacheControl = {\n                            revalidate: next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.CACHE_ONE_YEAR,\n                            expire: undefined\n                        };\n                    }\n                }\n            }\n            cacheEntry.cacheControl = cacheControl;\n            if (typeof segmentPrefetchHeader === 'string' && (cachedData == null ? void 0 : cachedData.kind) === next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE && cachedData.segmentData) {\n                var _cachedData_headers1;\n                // This is a prefetch request issued by the client Segment Cache. These\n                // should never reach the application layer (lambda). We should either\n                // respond from the cache (HIT) or respond with 204 No Content (MISS).\n                // Set a header to indicate that PPR is enabled for this route. This\n                // lets the client distinguish between a regular cache miss and a cache\n                // miss due to PPR being disabled. In other contexts this header is used\n                // to indicate that the response contains dynamic data, but here we're\n                // only using it to indicate that the feature is enabled — the segment\n                // response itself contains whether the data is dynamic.\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '2');\n                // Add the cache tags header to the response if it exists and we're in\n                // minimal mode while rendering a static page.\n                const tags = (_cachedData_headers1 = cachedData.headers) == null ? void 0 : _cachedData_headers1[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                    res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n                }\n                const matchedSegment = cachedData.segmentData.get(segmentPrefetchHeader);\n                if (matchedSegment !== undefined) {\n                    // Cache hit\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(matchedSegment),\n                        cacheControl: cacheEntry.cacheControl\n                    });\n                }\n                // Cache miss. Either a cache entry for this route has not been generated\n                // (which technically should not be possible when PPR is enabled, because\n                // at a minimum there should always be a fallback entry) or there's no\n                // match for the requested segment. Respond with a 204 No Content. We\n                // don't bother to respond with 404, because these requests are only\n                // issued as part of a prefetch.\n                res.statusCode = 204;\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(''),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If there's a callback for `onCacheEntry`, call it with the cache entry\n            // and the revalidate options.\n            const onCacheEntry = (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'onCacheEntry');\n            if (onCacheEntry) {\n                const finished = await onCacheEntry({\n                    ...cacheEntry,\n                    // TODO: remove this when upstream doesn't\n                    // always expect this value to be \"PAGE\"\n                    value: {\n                        ...cacheEntry.value,\n                        kind: 'PAGE'\n                    }\n                }, {\n                    url: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_4__.getRequestMeta)(req, 'initURL')\n                });\n                if (finished) {\n                    // TODO: maybe we have to end the request?\n                    return null;\n                }\n            }\n            // If the request has a postponed state and it's a resume request we\n            // should error.\n            if (didPostpone && minimalPostponed) {\n                throw Object.defineProperty(new Error('Invariant: postponed state should not be present on a resume request'), \"__NEXT_ERROR_CODE\", {\n                    value: \"E396\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (cachedData.headers) {\n                const headers = {\n                    ...cachedData.headers\n                };\n                if (!minimalMode || !isSSG) {\n                    delete headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n                }\n                for (let [key, value] of Object.entries(headers)){\n                    if (typeof value === 'undefined') continue;\n                    if (Array.isArray(value)) {\n                        for (const v of value){\n                            res.appendHeader(key, v);\n                        }\n                    } else if (typeof value === 'number') {\n                        value = value.toString();\n                        res.appendHeader(key, value);\n                    } else {\n                        res.appendHeader(key, value);\n                    }\n                }\n            }\n            // Add the cache tags header to the response if it exists and we're in\n            // minimal mode while rendering a static page.\n            const tags = (_cachedData_headers = cachedData.headers) == null ? void 0 : _cachedData_headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER];\n            if (minimalMode && isSSG && tags && typeof tags === 'string') {\n                res.setHeader(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_20__.NEXT_CACHE_TAGS_HEADER, tags);\n            }\n            // If the request is a data request, then we shouldn't set the status code\n            // from the response because it should always be 200. This should be gated\n            // behind the experimental PPR flag.\n            if (cachedData.status && (!isRSCRequest || !isRoutePPREnabled)) {\n                res.statusCode = cachedData.status;\n            }\n            // Redirect information is encoded in RSC payload, so we don't need to use redirect status codes\n            if (!minimalMode && cachedData.status && next_dist_client_components_redirect_status_code__WEBPACK_IMPORTED_MODULE_26__.RedirectStatusCode[cachedData.status] && isRSCRequest) {\n                res.statusCode = 200;\n            }\n            // Mark that the request did postpone.\n            if (didPostpone) {\n                res.setHeader(next_dist_client_components_app_router_headers__WEBPACK_IMPORTED_MODULE_15__.NEXT_DID_POSTPONE_HEADER, '1');\n            }\n            // we don't go through this block when preview mode is true\n            // as preview mode is a dynamic request (bypasses cache) and doesn't\n            // generate both HTML and payloads in the same request so continue to just\n            // return the generated payload\n            if (isRSCRequest && !isDraftMode) {\n                // If this is a dynamic RSC request, then stream the response.\n                if (typeof cachedData.rscData === 'undefined') {\n                    if (cachedData.postponed) {\n                        throw Object.defineProperty(new Error('Invariant: Expected postponed to be undefined'), \"__NEXT_ERROR_CODE\", {\n                            value: \"E372\",\n                            enumerable: false,\n                            configurable: true\n                        });\n                    }\n                    return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                        req,\n                        res,\n                        type: 'rsc',\n                        generateEtags: nextConfig.generateEtags,\n                        poweredByHeader: nextConfig.poweredByHeader,\n                        result: cachedData.html,\n                        // Dynamic RSC responses cannot be cached, even if they're\n                        // configured with `force-static` because we have no way of\n                        // distinguishing between `force-static` and pages that have no\n                        // postponed state.\n                        // TODO: distinguish `force-static` from pages with no postponed state (static)\n                        cacheControl: isDynamicRSCRequest ? {\n                            revalidate: 0,\n                            expire: undefined\n                        } : cacheEntry.cacheControl\n                    });\n                }\n                // As this isn't a prefetch request, we should serve the static flight\n                // data.\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'rsc',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: next_dist_server_render_result__WEBPACK_IMPORTED_MODULE_19__[\"default\"].fromStatic(cachedData.rscData),\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // This is a request for HTML data.\n            let body = cachedData.html;\n            // If there's no postponed state, we should just serve the HTML. This\n            // should also be the case for a resume request because it's completed\n            // as a server render (rather than a static render).\n            if (!didPostpone || minimalMode) {\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: cacheEntry.cacheControl\n                });\n            }\n            // If we're debugging the static shell or the dynamic API accesses, we\n            // should just serve the HTML without resuming the render. The returned\n            // HTML will be the static shell so all the Dynamic API's will be used\n            // during static generation.\n            if (isDebugStaticShell || isDebugDynamicAccesses) {\n                // Since we're not resuming the render, we need to at least add the\n                // closing body and html tags to create valid HTML.\n                body.chain(new ReadableStream({\n                    start (controller) {\n                        controller.enqueue(next_dist_server_stream_utils_encoded_tags__WEBPACK_IMPORTED_MODULE_21__.ENCODED_TAGS.CLOSED.BODY_AND_HTML);\n                        controller.close();\n                    }\n                }));\n                return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                    req,\n                    res,\n                    type: 'html',\n                    generateEtags: nextConfig.generateEtags,\n                    poweredByHeader: nextConfig.poweredByHeader,\n                    result: body,\n                    cacheControl: {\n                        revalidate: 0,\n                        expire: undefined\n                    }\n                });\n            }\n            // This request has postponed, so let's create a new transformer that the\n            // dynamic data can pipe to that will attach the dynamic data to the end\n            // of the response.\n            const transformer = new TransformStream();\n            body.chain(transformer.readable);\n            // Perform the render again, but this time, provide the postponed state.\n            // We don't await because we want the result to start streaming now, and\n            // we've already chained the transformer's readable to the render result.\n            doRender({\n                span,\n                postponed: cachedData.postponed,\n                // This is a resume render, not a fallback render, so we don't need to\n                // set this.\n                fallbackRouteParams: null\n            }).then(async (result)=>{\n                var _result_value;\n                if (!result) {\n                    throw Object.defineProperty(new Error('Invariant: expected a result to be returned'), \"__NEXT_ERROR_CODE\", {\n                        value: \"E463\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                if (((_result_value = result.value) == null ? void 0 : _result_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_17__.CachedRouteKind.APP_PAGE) {\n                    var _result_value1;\n                    throw Object.defineProperty(new Error(`Invariant: expected a page response, got ${(_result_value1 = result.value) == null ? void 0 : _result_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                        value: \"E305\",\n                        enumerable: false,\n                        configurable: true\n                    });\n                }\n                // Pipe the resume result to the transformer.\n                await result.value.html.pipeTo(transformer.writable);\n            }).catch((err)=>{\n                // An error occurred during piping or preparing the render, abort\n                // the transformers writer so we can terminate the stream.\n                transformer.writable.abort(err).catch((e)=>{\n                    console.error(\"couldn't abort transformer\", e);\n                });\n            });\n            return (0,next_dist_server_send_payload__WEBPACK_IMPORTED_MODULE_22__.sendRenderResult)({\n                req,\n                res,\n                type: 'html',\n                generateEtags: nextConfig.generateEtags,\n                poweredByHeader: nextConfig.poweredByHeader,\n                result: body,\n                // We don't want to cache the response if it has postponed data because\n                // the response being sent to the client it's dynamic parts are streamed\n                // to the client on the same request.\n                cacheControl: {\n                    revalidate: 0,\n                    expire: undefined\n                }\n            });\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            return await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_5__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_3__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_23__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: srcPage,\n                routeType: 'render',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_2__.getRevalidateReason)({\n                    isRevalidate: isSSG,\n                    isOnDemandRevalidate\n                })\n            }, routerServerContext);\n        }\n        // rethrow so that we can handle serving error page\n        throw err;\n    }\n}\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&page=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&appPaths=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&pagePath=private-next-app-dir%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage.tsx&appDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Fhide-if-empty.js%22%2C%22ids%22%3A%5B%22HideIfEmpty%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Flanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Froot-toggle.js%22%2C%22ids%22%3A%5B%22RootToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsearch-toggle.js%22%2C%22ids%22%3A%5B%22LargeSearchToggle%22%2C%22SearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsidebar.js%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarFolder%22%2C%22SidebarFolderLink%22%2C%22SidebarFolderTrigger%22%2C%22SidebarFolderContent%22%2C%22SidebarItem%22%2C%22SidebarViewport%22%2C%22SidebarPageTree%22%2C%22SidebarContentMobile%22%2C%22SidebarHeader%22%2C%22SidebarFooter%22%2C%22SidebarContent%22%2C%22SidebarCollapseTrigger%22%2C%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Flayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Ftree.js%22%2C%22ids%22%3A%5B%22TreeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs-client.js%22%2C%22ids%22%3A%5B%22SidebarTrigger%22%2C%22CollapsibleControl%22%2C%22Navbar%22%2C%22LayoutBody%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Flinks.js%22%2C%22ids%22%3A%5B%22BaseLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fprovider%2Findex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2F(docs)%2Fdocs.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fjotai-provider.tsx%22%2C%22ids%22%3A%5B%22JotaiProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Ftoaster-provider.tsx%22%2C%22ids%22%3A%5B%22ToasterProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Fhide-if-empty.js%22%2C%22ids%22%3A%5B%22HideIfEmpty%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Flanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Froot-toggle.js%22%2C%22ids%22%3A%5B%22RootToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsearch-toggle.js%22%2C%22ids%22%3A%5B%22LargeSearchToggle%22%2C%22SearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsidebar.js%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarFolder%22%2C%22SidebarFolderLink%22%2C%22SidebarFolderTrigger%22%2C%22SidebarFolderContent%22%2C%22SidebarItem%22%2C%22SidebarViewport%22%2C%22SidebarPageTree%22%2C%22SidebarContentMobile%22%2C%22SidebarHeader%22%2C%22SidebarFooter%22%2C%22SidebarContent%22%2C%22SidebarCollapseTrigger%22%2C%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Flayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Ftree.js%22%2C%22ids%22%3A%5B%22TreeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs-client.js%22%2C%22ids%22%3A%5B%22SidebarTrigger%22%2C%22CollapsibleControl%22%2C%22Navbar%22%2C%22LayoutBody%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Flinks.js%22%2C%22ids%22%3A%5B%22BaseLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fprovider%2Findex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2F(docs)%2Fdocs.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fjotai-provider.tsx%22%2C%22ids%22%3A%5B%22JotaiProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Ftoaster-provider.tsx%22%2C%22ids%22%3A%5B%22ToasterProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-core/dist/hide-if-empty.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/hide-if-empty.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-core/dist/link.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/contexts/layout.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/contexts/layout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/contexts/tree.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/contexts/tree.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/layouts/docs-client.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/layouts/links.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/links.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/provider/index.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/provider/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/jotai-provider.tsx */ \"(rsc)/./components/providers/jotai-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/query-provider.tsx */ \"(rsc)/./components/providers/query-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/toaster-provider.tsx */ \"(rsc)/./components/providers/toaster-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Fhide-if-empty.js%22%2C%22ids%22%3A%5B%22HideIfEmpty%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Flanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Froot-toggle.js%22%2C%22ids%22%3A%5B%22RootToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsearch-toggle.js%22%2C%22ids%22%3A%5B%22LargeSearchToggle%22%2C%22SearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsidebar.js%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarFolder%22%2C%22SidebarFolderLink%22%2C%22SidebarFolderTrigger%22%2C%22SidebarFolderContent%22%2C%22SidebarItem%22%2C%22SidebarViewport%22%2C%22SidebarPageTree%22%2C%22SidebarContentMobile%22%2C%22SidebarHeader%22%2C%22SidebarFooter%22%2C%22SidebarContent%22%2C%22SidebarCollapseTrigger%22%2C%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Flayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Ftree.js%22%2C%22ids%22%3A%5B%22TreeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs-client.js%22%2C%22ids%22%3A%5B%22SidebarTrigger%22%2C%22CollapsibleControl%22%2C%22Navbar%22%2C%22LayoutBody%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Flinks.js%22%2C%22ids%22%3A%5B%22BaseLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fprovider%2Findex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2F(docs)%2Fdocs.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fjotai-provider.tsx%22%2C%22ids%22%3A%5B%22JotaiProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Ftoaster-provider.tsx%22%2C%22ids%22%3A%5B%22ToasterProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc-clerk.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc.js%22%2C%22ids%22%3A%5B%22TOCScrollArea%22%2C%22TOCItems%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Fi18n.js%22%2C%22ids%22%3A%5B%22*%22%2C%22I18nLabel%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs%2Fpage-client.js%22%2C%22ids%22%3A%5B%22PageRoot%22%2C%22PageBreadcrumb%22%2C%22PageFooter%22%2C%22PageLastUpdate%22%2C%22PageTOC%22%2C%22PageTOCPopover%22%2C%22PageTOCPopoverTrigger%22%2C%22PageTOCPopoverContent%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc-clerk.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc.js%22%2C%22ids%22%3A%5B%22TOCScrollArea%22%2C%22TOCItems%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Fi18n.js%22%2C%22ids%22%3A%5B%22*%22%2C%22I18nLabel%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs%2Fpage-client.js%22%2C%22ids%22%3A%5B%22PageRoot%22%2C%22PageBreadcrumb%22%2C%22PageFooter%22%2C%22PageLastUpdate%22%2C%22PageTOC%22%2C%22PageTOCPopover%22%2C%22PageTOCPopoverTrigger%22%2C%22PageTOCPopoverContent%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-core/dist/link.js */ \"(rsc)/../../node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/toc.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/contexts/i18n.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js */ \"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc-clerk.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc.js%22%2C%22ids%22%3A%5B%22TOCScrollArea%22%2C%22TOCItems%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Fi18n.js%22%2C%22ids%22%3A%5B%22*%22%2C%22I18nLabel%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs%2Fpage-client.js%22%2C%22ids%22%3A%5B%22PageRoot%22%2C%22PageBreadcrumb%22%2C%22PageFooter%22%2C%22PageLastUpdate%22%2C%22PageTOC%22%2C%22PageTOCPopover%22%2C%22PageTOCPopoverTrigger%22%2C%22PageTOCPopoverContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(rsc)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/../../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/../../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(rsc)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(rsc)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/global-error.tsx */ \"(rsc)/./app/global-error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZwYWNrYWdlcyUyRndlYiUyRmFwcCUyRmdsb2JhbC1lcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL2FwcC9nbG9iYWwtZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./app/(docs)/docs.css":
/*!*****************************!*\
  !*** ./app/(docs)/docs.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"cf5b06722e5e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvKGRvY3MpL2RvY3MuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9wYWNrYWdlcy93ZWIvYXBwLyhkb2NzKS9kb2NzLmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImNmNWIwNjcyMmU1ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/(docs)/docs.css\n");

/***/ }),

/***/ "(rsc)/./app/(docs)/docs/[[...slug]]/page.tsx":
/*!**********************************************!*\
  !*** ./app/(docs)/docs/[[...slug]]/page.tsx ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateStaticParams: () => (/* binding */ generateStaticParams)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_source__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/source */ \"(rsc)/./lib/source.ts\");\n/* harmony import */ var fumadocs_ui_page__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fumadocs-ui/page */ \"(rsc)/../../node_modules/fumadocs-ui/dist/page.server.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(rsc)/../../node_modules/next/dist/api/navigation.react-server.js\");\n\n\n\n\nasync function Page({ params }) {\n    const { slug } = await params;\n    const page = _lib_source__WEBPACK_IMPORTED_MODULE_1__.source.getPage(slug || [\n        'index'\n    ]);\n    if (!page) {\n        (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.notFound)();\n    }\n    const MDX = page.data.body;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_page__WEBPACK_IMPORTED_MODULE_2__.DocsPage, {\n        toc: page.data.toc,\n        full: page.data.full,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_page__WEBPACK_IMPORTED_MODULE_2__.DocsBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: page.data.title\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MDX, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/docs/[[...slug]]/page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\nasync function generateStaticParams() {\n    return _lib_source__WEBPACK_IMPORTED_MODULE_1__.source.generateParams();\n}\nasync function generateMetadata({ params }) {\n    const { slug } = await params;\n    const page = _lib_source__WEBPACK_IMPORTED_MODULE_1__.source.getPage(slug || [\n        'index'\n    ]);\n    if (!page) return {};\n    return {\n        title: page.data.title,\n        description: page.data.description\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/(docs)/docs/[[...slug]]/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/(docs)/layout.tsx":
/*!*******************************!*\
  !*** ./app/(docs)/layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   dynamic: () => (/* binding */ dynamic),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   revalidate: () => (/* binding */ revalidate),\n/* harmony export */   runtime: () => (/* binding */ runtime),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_source__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/source */ \"(rsc)/./lib/source.ts\");\n/* harmony import */ var fumadocs_ui_layouts_docs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! fumadocs-ui/layouts/docs */ \"(rsc)/../../node_modules/fumadocs-ui/dist/layouts/docs.js\");\n/* harmony import */ var fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! fumadocs-ui/provider */ \"(rsc)/../../node_modules/fumadocs-ui/dist/provider/index.js\");\n/* harmony import */ var _components_providers_jotai_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/jotai-provider */ \"(rsc)/./components/providers/jotai-provider.tsx\");\n/* harmony import */ var _components_providers_query_provider__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/providers/query-provider */ \"(rsc)/./components/providers/query-provider.tsx\");\n/* harmony import */ var _components_providers_toaster_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/providers/toaster-provider */ \"(rsc)/./components/providers/toaster-provider.tsx\");\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _docs_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./docs.css */ \"(rsc)/./app/(docs)/docs.css\");\n\n\n\n\n\n\n\n\n\nconst dynamic = 'force-dynamic';\nconst revalidate = 0;\nconst runtime = 'nodejs';\nconst metadata = {\n    metadataBase: new URL(\"https://onlyrules.codes\" || 0),\n    title: 'OnlyRules - AI Prompt Management Platform',\n    description: 'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',\n    keywords: 'AI, IDE, prompt engineering, coding, productivity, Cursor, Augment Code, Windsurf, Claude, GitHub Copilot, Gemini, OpenAI Codex, Cline, Junie, Trae, Lingma, Kiro, Tencent Cloud CodeBuddy',\n    authors: [\n        {\n            name: 'OnlyRules Team'\n        }\n    ],\n    publisher: 'OnlyRules',\n    category: 'Technology',\n    classification: 'Software Development Tools',\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            'max-video-preview': -1,\n            'max-image-preview': 'large',\n            'max-snippet': -1\n        }\n    },\n    openGraph: {\n        title: 'OnlyRules - AI Prompt Management Platform',\n        description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',\n        type: 'website',\n        locale: 'en_US',\n        siteName: 'OnlyRules'\n    },\n    twitter: {\n        card: 'summary_large_image',\n        title: 'OnlyRules - AI Prompt Management Platform',\n        description: 'Create, organize, and share AI prompt rules for your favorite IDEs.'\n    },\n    alternates: {\n        canonical: \"https://onlyrules.codes\" || 0\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    maximumScale: 5,\n    userScalable: true,\n    themeColor: [\n        {\n            media: '(prefers-color-scheme: light)',\n            color: 'hsl(0 0% 100%)'\n        },\n        {\n            media: '(prefers-color-scheme: dark)',\n            color: 'hsl(240 10% 3.9%)'\n        }\n    ]\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            suppressHydrationWarning: true,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_jotai_provider__WEBPACK_IMPORTED_MODULE_4__.JotaiProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_query_provider__WEBPACK_IMPORTED_MODULE_5__.QueryProvider, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_provider__WEBPACK_IMPORTED_MODULE_3__.RootProvider, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(fumadocs_ui_layouts_docs__WEBPACK_IMPORTED_MODULE_2__.DocsLayout, {\n                                tree: {\n                                    name: 'Docs',\n                                    children: _lib_source__WEBPACK_IMPORTED_MODULE_1__.source.pageTree\n                                },\n                                nav: {\n                                    title: 'OnlyRules Docs',\n                                    url: '/docs'\n                                },\n                                sidebar: {\n                                    defaultOpenLevel: 1\n                                },\n                                links: [\n                                    {\n                                        text: 'Home',\n                                        url: '/'\n                                    },\n                                    {\n                                        text: 'Dashboard',\n                                        url: '/dashboard'\n                                    },\n                                    {\n                                        text: 'GitHub',\n                                        url: 'https://github.com/ranglang/onlyrules',\n                                        external: true\n                                    }\n                                ],\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 9\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n                            lineNumber: 72,\n                            columnNumber: 7\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_providers_toaster_provider__WEBPACK_IMPORTED_MODULE_6__.ToasterProvider, {}, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n                    lineNumber: 70,\n                    columnNumber: 13\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n                lineNumber: 69,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n            lineNumber: 68,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/(docs)/layout.tsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/(docs)/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/global-error.tsx":
/*!******************************!*\
  !*** ./app/global-error.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call the default export of \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx",
"default",
));


/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fe96d3eb5da3\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9hcHAvZ2xvYmFscy5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmZTk2ZDNlYjVkYTNcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./components/providers/jotai-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/jotai-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   JotaiProvider: () => (/* binding */ JotaiProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const JotaiProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call JotaiProvider() from the server but JotaiProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/jotai-provider.tsx",
"JotaiProvider",
);

/***/ }),

/***/ "(rsc)/./components/providers/query-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/query-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const QueryProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/query-provider.tsx",
"QueryProvider",
);

/***/ }),

/***/ "(rsc)/./components/providers/toaster-provider.tsx":
/*!***************************************************!*\
  !*** ./components/providers/toaster-provider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ToasterProvider: () => (/* binding */ ToasterProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__);

const ToasterProvider = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ToasterProvider() from the server but ToasterProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/toaster-provider.tsx",
"ToasterProvider",
);

/***/ }),

/***/ "(rsc)/./lib/docs-content.tsx":
/*!******************************!*\
  !*** ./lib/docs-content.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DocsContent: () => (/* binding */ DocsContent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/../../node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Simple content components for documentation\nconst DocsContent = {\n    index: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"prose prose-slate max-w-none dark:prose-invert\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold text-foreground mb-6\",\n                    children: \"Welcome to OnlyRules Documentation\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg text-muted-foreground leading-relaxed mb-8\",\n                    children: \"OnlyRules is an AI Prompt Management Platform that helps developers create, organize, and share AI prompt rules for their favorite IDEs.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-foreground mt-8 mb-4\",\n                    children: \"Quick Start\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground mb-4\",\n                    children: \"Get started with OnlyRules in just a few minutes:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 12,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    className: \"list-decimal list-inside space-y-2 ml-4 text-muted-foreground\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Sign up\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 14,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" for an account at OnlyRules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Browse\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" existing templates and rules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Create\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" your own custom rules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Share\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" with the community\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 17,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-foreground mt-8 mb-4\",\n                    children: \"Features\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 20,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc list-inside space-y-2 ml-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"\\uD83D\\uDE80 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"IDE Integration\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 50\n                                }, undefined),\n                                \" - Works with Cursor, Windsurf, GitHub Copilot, Claude, and more\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 22,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"\\uD83D\\uDCDD \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Rule Management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 23,\n                                    columnNumber: 50\n                                }, undefined),\n                                \" - Create, edit, and organize your AI prompt rules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 23,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"\\uD83C\\uDF10 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Community Sharing\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 24,\n                                    columnNumber: 50\n                                }, undefined),\n                                \" - Share and discover rules from other developers\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"\\uD83C\\uDFA8 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Template System\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 25,\n                                    columnNumber: 50\n                                }, undefined),\n                                \" - Use pre-built templates to get started quickly\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 25,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            className: \"text-muted-foreground\",\n                            children: [\n                                \"\\uD83D\\uDD0D \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    className: \"text-foreground\",\n                                    children: \"Search & Discovery\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 26,\n                                    columnNumber: 50\n                                }, undefined),\n                                \" - Find the perfect rules for your use case\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 26,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-semibold text-foreground mt-8 mb-4\",\n                    children: \"Popular IDEs Supported\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc list-inside space-y-2 ml-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/docs/ides/cursor\",\n                                className: \"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline\",\n                                children: \"Cursor\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/docs/ides/windsurf\",\n                                className: \"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline\",\n                                children: \"Windsurf\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/docs/ides/github-copilot\",\n                                className: \"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline\",\n                                children: \"GitHub Copilot\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/docs/ides/claude\",\n                                className: \"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline\",\n                                children: \"Claude\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/docs/ides/cline\",\n                                className: \"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline\",\n                                children: \"Cline\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: \"/docs/ides/augment\",\n                                className: \"text-primary hover:text-primary/80 transition-colors underline-offset-4 hover:underline\",\n                                children: \"Augment Code\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 7,\n            columnNumber: 5\n        }, undefined),\n    'getting-started/quick-start': ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Quick Start Guide\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Welcome to OnlyRules! This guide will help you get started with creating and using AI prompt rules for your favorite IDEs.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"What is OnlyRules?\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 46,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"OnlyRules is an AI Prompt Management Platform that helps developers:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Create custom AI prompt rules for different IDEs\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Share and discover community-created rules\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Organize rules with tags and categories\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Export rules in various formats\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Step 1: Create an Account\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 55,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"Visit \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/\",\n                                    children: \"OnlyRules\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"Click \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Sign Up\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 19\n                                }, undefined),\n                                \" in the navigation\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Fill in your details and create your account\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Verify your email address\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Step 2: Browse Existing Rules\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Before creating your own rules, explore what's already available:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"Go to the \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"/dashboard\",\n                                    children: \"Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 66,\n                                    columnNumber: 23\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Browse rules by IDE type or tags\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Click on any rule to view its content\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Use the search functionality to find specific rules\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 42,\n            columnNumber: 5\n        }, undefined),\n    'ides/cursor': ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Cursor IDE Integration\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Cursor is an AI-powered code editor that makes it easy to integrate custom AI prompt rules. This guide shows you how to use OnlyRules with Cursor.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"What is Cursor?\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Cursor is a fork of VS Code that's designed from the ground up to be the best way to code with AI. It features:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Built-in AI chat and code generation\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Custom rule support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Advanced AI features like Composer and Tab completion\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Full VS Code compatibility\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Setting Up Rules in Cursor\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"Method 1: Using .cursorrules File\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Create a .cursorrules file\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" in your project root\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 91,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Copy rule content\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" from OnlyRules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Paste into .cursorrules\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" file\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Save the file\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" - Cursor will automatically detect it\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 75,\n            columnNumber: 5\n        }, undefined),\n    'api/overview': ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"API Overview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"The OnlyRules API provides programmatic access to rules, templates, and user data. All API endpoints are RESTful and return JSON responses.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Base URL\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: \"https://onlyrules.app/api\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 12\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Authentication\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Most API endpoints require authentication. Include your API key in the Authorization header:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: `curl -H \"Authorization: Bearer YOUR_API_KEY\" \\\\\n  https://onlyrules.app/api/rules`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 12\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Available Endpoints\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"Rules API\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: \"GET /api/rules\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" - List all rules\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: [\n                                        \"GET /api/rules/\",\n                                        '{id}'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" - Get a specific rule\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: \"POST /api/rules\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" - Create a new rule\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: [\n                                        \"PUT /api/rules/\",\n                                        '{id}'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" - Update a rule\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: [\n                                        \"DELETE /api/rules/\",\n                                        '{id}'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 119,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" - Delete a rule\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 100,\n            columnNumber: 5\n        }, undefined),\n    'guides/seo-setup': ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"SEO Setup for OnlyRules\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"This document outlines the SEO enhancements implemented for the OnlyRules Next.js application.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Implemented Features\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"1. Dynamic Sitemap Generation\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 130,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                \"Automatically generates a sitemap at \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                    children: \"/sitemap.xml\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                                    lineNumber: 132,\n                                    columnNumber: 50\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Uses API routes for Next.js compatibility\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Includes all static routes with appropriate priorities\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"2. Robots.txt Configuration\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Dynamically generates robots.txt\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Allows crawlers to access public pages\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Disallows access to private directories\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 141,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 125,\n            columnNumber: 5\n        }, undefined),\n    'guides/i18n': ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Internationalization (i18n) Guide\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"This project uses Lingui.js for internationalization, supporting English (en), Simplified Chinese (zh-CN), and Traditional Chinese (zh-HK).\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 149,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Quick Start\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"Using Translations in Components\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"Use the \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            children: \"useLingui\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 18\n                        }, undefined),\n                        \" hook from \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            children: \"@lingui/react\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 51\n                        }, undefined),\n                        \":\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: `'use client'\n\nimport { useLingui } from '@lingui/react'\n\nexport function MyComponent() {\n  const { i18n } = useLingui()\n  \n  return (\n    <div>\n      <h1>{i18n._(\"hero.title\")}</h1>\n      <p>{i18n._(\"hero.description\")}</p>\n    </div>\n  )\n}`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 12\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 154,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 147,\n            columnNumber: 5\n        }, undefined),\n    'guides/radix-ui-theme': ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    children: \"Radix UI Theme v3 Guide\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"This guide explains how to use Radix UI Theme v3 in the OnlyRules project.\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 174,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Overview\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"We've migrated from a mixed Tailwind/custom CSS approach to using Radix UI Theme v3's design system. This provides:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 177,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Consistent design tokens\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Built-in dark mode support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Accessible components\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Responsive design patterns\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 9\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: \"Better performance\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 9\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 178,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    children: \"Key Changes\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    children: \"1. Component Usage\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: \"Instead of using HTML elements with Tailwind classes, use Radix UI components:\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 188,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        children: `// ❌ Old approach\n<div className=\"flex flex-col gap-4\">\n  <h1 className=\"text-4xl font-bold\">Title</h1>\n  <p className=\"text-gray-600\">Description</p>\n</div>\n\n// ✅ New approach\n<Flex direction=\"column\" gap=\"4\">\n  <Heading size=\"8\">Title</Heading>\n  <Text color=\"gray\">Description</Text>\n</Flex>`\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                        lineNumber: 189,\n                        columnNumber: 12\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n                    lineNumber: 189,\n                    columnNumber: 7\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/lib/docs-content.tsx\",\n            lineNumber: 172,\n            columnNumber: 5\n        }, undefined)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/docs-content.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/source.ts":
/*!***********************!*\
  !*** ./lib/source.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   source: () => (/* binding */ source)\n/* harmony export */ });\n// Simple mock source for build compatibility\nconst source = {\n    pageTree: [\n        {\n            type: 'page',\n            name: 'Documentation',\n            url: '/docs',\n            children: [\n                {\n                    type: 'page',\n                    name: 'Getting Started',\n                    url: '/docs/getting-started',\n                    children: [\n                        {\n                            type: 'page',\n                            name: 'Quick Start',\n                            url: '/docs/getting-started/quick-start',\n                            children: []\n                        }\n                    ]\n                },\n                {\n                    type: 'page',\n                    name: 'IDE Guides',\n                    url: '/docs/ides',\n                    children: [\n                        {\n                            type: 'page',\n                            name: 'Cursor',\n                            url: '/docs/ides/cursor',\n                            children: []\n                        }\n                    ]\n                },\n                {\n                    type: 'page',\n                    name: 'API Reference',\n                    url: '/docs/api',\n                    children: [\n                        {\n                            type: 'page',\n                            name: 'Overview',\n                            url: '/docs/api/overview',\n                            children: []\n                        }\n                    ]\n                },\n                {\n                    type: 'page',\n                    name: 'Guides',\n                    url: '/docs/guides',\n                    children: [\n                        {\n                            type: 'page',\n                            name: 'SEO Setup',\n                            url: '/docs/guides/seo-setup',\n                            children: []\n                        },\n                        {\n                            type: 'page',\n                            name: 'Internationalization',\n                            url: '/docs/guides/i18n',\n                            children: []\n                        },\n                        {\n                            type: 'page',\n                            name: 'Radix UI Theme',\n                            url: '/docs/guides/radix-ui-theme',\n                            children: []\n                        }\n                    ]\n                }\n            ]\n        }\n    ],\n    getPage: (slug)=>{\n        const { DocsContent } = __webpack_require__(/*! ./docs-content.tsx */ \"(rsc)/./lib/docs-content.tsx\");\n        const path = slug ? slug.join('/') : 'index';\n        const pageData = {\n            'index': {\n                title: 'Welcome to OnlyRules Documentation',\n                description: 'Learn how to use OnlyRules AI Prompt Management Platform',\n                body: DocsContent.index\n            },\n            'getting-started/quick-start': {\n                title: 'Quick Start Guide',\n                description: 'Get started with OnlyRules in just a few minutes',\n                body: DocsContent['getting-started/quick-start']\n            },\n            'ides/cursor': {\n                title: 'Cursor IDE Integration',\n                description: 'Learn how to use OnlyRules with Cursor IDE',\n                body: DocsContent['ides/cursor']\n            },\n            'api/overview': {\n                title: 'API Overview',\n                description: 'Learn about the OnlyRules API endpoints and how to use them',\n                body: DocsContent['api/overview']\n            },\n            'guides/seo-setup': {\n                title: 'SEO Setup for OnlyRules',\n                description: 'Learn how to configure SEO enhancements for the OnlyRules Next.js application',\n                body: DocsContent['guides/seo-setup']\n            },\n            'guides/i18n': {\n                title: 'Internationalization (i18n) Guide',\n                description: 'Learn how to use Lingui.js for internationalization in OnlyRules',\n                body: DocsContent['guides/i18n']\n            },\n            'guides/radix-ui-theme': {\n                title: 'Radix UI Theme v3 Guide',\n                description: 'Learn how to use Radix UI Theme v3 in the OnlyRules project',\n                body: DocsContent['guides/radix-ui-theme']\n            }\n        };\n        const page = pageData[path];\n        if (!page) return null;\n        return {\n            data: {\n                title: page.title,\n                description: page.description,\n                body: page.body,\n                toc: [],\n                full: false,\n                structuredData: {\n                    title: page.title,\n                    description: page.description\n                }\n            }\n        };\n    },\n    generateParams: ()=>{\n        return [\n            {\n                slug: [\n                    'index'\n                ]\n            },\n            {\n                slug: [\n                    'getting-started',\n                    'quick-start'\n                ]\n            },\n            {\n                slug: [\n                    'ides',\n                    'cursor'\n                ]\n            },\n            {\n                slug: [\n                    'api',\n                    'overview'\n                ]\n            },\n            {\n                slug: [\n                    'guides',\n                    'seo-setup'\n                ]\n            },\n            {\n                slug: [\n                    'guides',\n                    'i18n'\n                ]\n            },\n            {\n                slug: [\n                    'guides',\n                    'radix-ui-theme'\n                ]\n            }\n        ];\n    },\n    getPages: ()=>{\n        return [\n            {\n                slug: [\n                    'index'\n                ]\n            },\n            {\n                slug: [\n                    'getting-started',\n                    'quick-start'\n                ]\n            },\n            {\n                slug: [\n                    'ides',\n                    'cursor'\n                ]\n            },\n            {\n                slug: [\n                    'api',\n                    'overview'\n                ]\n            },\n            {\n                slug: [\n                    'guides',\n                    'seo-setup'\n                ]\n            },\n            {\n                slug: [\n                    'guides',\n                    'i18n'\n                ]\n            },\n            {\n                slug: [\n                    'guides',\n                    'radix-ui-theme'\n                ]\n            }\n        ];\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvc291cmNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSw2Q0FBNkM7QUFDdEMsTUFBTUEsU0FBUztJQUNwQkMsVUFBVTtRQUNSO1lBQ0VDLE1BQU07WUFDTkMsTUFBTTtZQUNOQyxLQUFLO1lBQ0xDLFVBQVU7Z0JBQ1I7b0JBQ0VILE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLEtBQUs7b0JBQ0xDLFVBQVU7d0JBQ1I7NEJBQ0VILE1BQU07NEJBQ05DLE1BQU07NEJBQ05DLEtBQUs7NEJBQ0xDLFVBQVUsRUFBRTt3QkFDZDtxQkFDRDtnQkFDSDtnQkFDQTtvQkFDRUgsTUFBTTtvQkFDTkMsTUFBTTtvQkFDTkMsS0FBSztvQkFDTEMsVUFBVTt3QkFDUjs0QkFDRUgsTUFBTTs0QkFDTkMsTUFBTTs0QkFDTkMsS0FBSzs0QkFDTEMsVUFBVSxFQUFFO3dCQUNkO3FCQUNEO2dCQUNIO2dCQUNBO29CQUNFSCxNQUFNO29CQUNOQyxNQUFNO29CQUNOQyxLQUFLO29CQUNMQyxVQUFVO3dCQUNSOzRCQUNFSCxNQUFNOzRCQUNOQyxNQUFNOzRCQUNOQyxLQUFLOzRCQUNMQyxVQUFVLEVBQUU7d0JBQ2Q7cUJBQ0Q7Z0JBQ0g7Z0JBQ0E7b0JBQ0VILE1BQU07b0JBQ05DLE1BQU07b0JBQ05DLEtBQUs7b0JBQ0xDLFVBQVU7d0JBQ1I7NEJBQ0VILE1BQU07NEJBQ05DLE1BQU07NEJBQ05DLEtBQUs7NEJBQ0xDLFVBQVUsRUFBRTt3QkFDZDt3QkFDQTs0QkFDRUgsTUFBTTs0QkFDTkMsTUFBTTs0QkFDTkMsS0FBSzs0QkFDTEMsVUFBVSxFQUFFO3dCQUNkO3dCQUNBOzRCQUNFSCxNQUFNOzRCQUNOQyxNQUFNOzRCQUNOQyxLQUFLOzRCQUNMQyxVQUFVLEVBQUU7d0JBQ2Q7cUJBQ0Q7Z0JBQ0g7YUFDRDtRQUNIO0tBQ0Q7SUFDREMsU0FBUyxDQUFDQztRQUNSLE1BQU0sRUFBRUMsV0FBVyxFQUFFLEdBQUdDLG1CQUFPQSxDQUFDLHdEQUFvQjtRQUNwRCxNQUFNQyxPQUFPSCxPQUFPQSxLQUFLSSxJQUFJLENBQUMsT0FBTztRQUVyQyxNQUFNQyxXQUFnQztZQUNwQyxTQUFTO2dCQUNQQyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxNQUFNUCxZQUFZUSxLQUFLO1lBQ3pCO1lBQ0EsK0JBQStCO2dCQUM3QkgsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsTUFBTVAsV0FBVyxDQUFDLDhCQUE4QjtZQUNsRDtZQUNBLGVBQWU7Z0JBQ2JLLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLE1BQU1QLFdBQVcsQ0FBQyxjQUFjO1lBQ2xDO1lBQ0EsZ0JBQWdCO2dCQUNkSyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxNQUFNUCxXQUFXLENBQUMsZUFBZTtZQUNuQztZQUNBLG9CQUFvQjtnQkFDbEJLLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLE1BQU1QLFdBQVcsQ0FBQyxtQkFBbUI7WUFDdkM7WUFDQSxlQUFlO2dCQUNiSyxPQUFPO2dCQUNQQyxhQUFhO2dCQUNiQyxNQUFNUCxXQUFXLENBQUMsY0FBYztZQUNsQztZQUNBLHlCQUF5QjtnQkFDdkJLLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLE1BQU1QLFdBQVcsQ0FBQyx3QkFBd0I7WUFDNUM7UUFDRjtRQUVBLE1BQU1TLE9BQU9MLFFBQVEsQ0FBQ0YsS0FBSztRQUMzQixJQUFJLENBQUNPLE1BQU0sT0FBTztRQUVsQixPQUFPO1lBQ0xDLE1BQU07Z0JBQ0pMLE9BQU9JLEtBQUtKLEtBQUs7Z0JBQ2pCQyxhQUFhRyxLQUFLSCxXQUFXO2dCQUM3QkMsTUFBTUUsS0FBS0YsSUFBSTtnQkFDZkksS0FBSyxFQUFFO2dCQUNQQyxNQUFNO2dCQUNOQyxnQkFBZ0I7b0JBQ2RSLE9BQU9JLEtBQUtKLEtBQUs7b0JBQ2pCQyxhQUFhRyxLQUFLSCxXQUFXO2dCQUMvQjtZQUNGO1FBQ0Y7SUFDRjtJQUNBUSxnQkFBZ0I7UUFDZCxPQUFPO1lBQ0w7Z0JBQUVmLE1BQU07b0JBQUM7aUJBQVE7WUFBQztZQUNsQjtnQkFBRUEsTUFBTTtvQkFBQztvQkFBbUI7aUJBQWM7WUFBQztZQUMzQztnQkFBRUEsTUFBTTtvQkFBQztvQkFBUTtpQkFBUztZQUFDO1lBQzNCO2dCQUFFQSxNQUFNO29CQUFDO29CQUFPO2lCQUFXO1lBQUM7WUFDNUI7Z0JBQUVBLE1BQU07b0JBQUM7b0JBQVU7aUJBQVk7WUFBQztZQUNoQztnQkFBRUEsTUFBTTtvQkFBQztvQkFBVTtpQkFBTztZQUFDO1lBQzNCO2dCQUFFQSxNQUFNO29CQUFDO29CQUFVO2lCQUFpQjtZQUFDO1NBQ3RDO0lBQ0g7SUFDQWdCLFVBQVU7UUFDUixPQUFPO1lBQ0w7Z0JBQUVoQixNQUFNO29CQUFDO2lCQUFRO1lBQUM7WUFDbEI7Z0JBQUVBLE1BQU07b0JBQUM7b0JBQW1CO2lCQUFjO1lBQUM7WUFDM0M7Z0JBQUVBLE1BQU07b0JBQUM7b0JBQVE7aUJBQVM7WUFBQztZQUMzQjtnQkFBRUEsTUFBTTtvQkFBQztvQkFBTztpQkFBVztZQUFDO1lBQzVCO2dCQUFFQSxNQUFNO29CQUFDO29CQUFVO2lCQUFZO1lBQUM7WUFDaEM7Z0JBQUVBLE1BQU07b0JBQUM7b0JBQVU7aUJBQU87WUFBQztZQUMzQjtnQkFBRUEsTUFBTTtvQkFBQztvQkFBVTtpQkFBaUI7WUFBQztTQUN0QztJQUNIO0FBQ0YsRUFBRSIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL2xpYi9zb3VyY2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gU2ltcGxlIG1vY2sgc291cmNlIGZvciBidWlsZCBjb21wYXRpYmlsaXR5XG5leHBvcnQgY29uc3Qgc291cmNlID0ge1xuICBwYWdlVHJlZTogW1xuICAgIHtcbiAgICAgIHR5cGU6ICdwYWdlJyxcbiAgICAgIG5hbWU6ICdEb2N1bWVudGF0aW9uJyxcbiAgICAgIHVybDogJy9kb2NzJyxcbiAgICAgIGNoaWxkcmVuOiBbXG4gICAgICAgIHtcbiAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgbmFtZTogJ0dldHRpbmcgU3RhcnRlZCcsXG4gICAgICAgICAgdXJsOiAnL2RvY3MvZ2V0dGluZy1zdGFydGVkJyxcbiAgICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgICAgIG5hbWU6ICdRdWljayBTdGFydCcsXG4gICAgICAgICAgICAgIHVybDogJy9kb2NzL2dldHRpbmctc3RhcnRlZC9xdWljay1zdGFydCcsXG4gICAgICAgICAgICAgIGNoaWxkcmVuOiBbXVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF1cbiAgICAgICAgfSxcbiAgICAgICAge1xuICAgICAgICAgIHR5cGU6ICdwYWdlJyxcbiAgICAgICAgICBuYW1lOiAnSURFIEd1aWRlcycsXG4gICAgICAgICAgdXJsOiAnL2RvY3MvaWRlcycsXG4gICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgdHlwZTogJ3BhZ2UnLFxuICAgICAgICAgICAgICBuYW1lOiAnQ3Vyc29yJyxcbiAgICAgICAgICAgICAgdXJsOiAnL2RvY3MvaWRlcy9jdXJzb3InLFxuICAgICAgICAgICAgICBjaGlsZHJlbjogW11cbiAgICAgICAgICAgIH1cbiAgICAgICAgICBdXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgbmFtZTogJ0FQSSBSZWZlcmVuY2UnLFxuICAgICAgICAgIHVybDogJy9kb2NzL2FwaScsXG4gICAgICAgICAgY2hpbGRyZW46IFtcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgdHlwZTogJ3BhZ2UnLFxuICAgICAgICAgICAgICBuYW1lOiAnT3ZlcnZpZXcnLFxuICAgICAgICAgICAgICB1cmw6ICcvZG9jcy9hcGkvb3ZlcnZpZXcnLFxuICAgICAgICAgICAgICBjaGlsZHJlbjogW11cbiAgICAgICAgICAgIH1cbiAgICAgICAgICBdXG4gICAgICAgIH0sXG4gICAgICAgIHtcbiAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgbmFtZTogJ0d1aWRlcycsXG4gICAgICAgICAgdXJsOiAnL2RvY3MvZ3VpZGVzJyxcbiAgICAgICAgICBjaGlsZHJlbjogW1xuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgICAgIG5hbWU6ICdTRU8gU2V0dXAnLFxuICAgICAgICAgICAgICB1cmw6ICcvZG9jcy9ndWlkZXMvc2VvLXNldHVwJyxcbiAgICAgICAgICAgICAgY2hpbGRyZW46IFtdXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgICAgIG5hbWU6ICdJbnRlcm5hdGlvbmFsaXphdGlvbicsXG4gICAgICAgICAgICAgIHVybDogJy9kb2NzL2d1aWRlcy9pMThuJyxcbiAgICAgICAgICAgICAgY2hpbGRyZW46IFtdXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAge1xuICAgICAgICAgICAgICB0eXBlOiAncGFnZScsXG4gICAgICAgICAgICAgIG5hbWU6ICdSYWRpeCBVSSBUaGVtZScsXG4gICAgICAgICAgICAgIHVybDogJy9kb2NzL2d1aWRlcy9yYWRpeC11aS10aGVtZScsXG4gICAgICAgICAgICAgIGNoaWxkcmVuOiBbXVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF1cbiAgICAgICAgfVxuICAgICAgXVxuICAgIH1cbiAgXSxcbiAgZ2V0UGFnZTogKHNsdWc6IHN0cmluZ1tdIHwgdW5kZWZpbmVkKSA9PiB7XG4gICAgY29uc3QgeyBEb2NzQ29udGVudCB9ID0gcmVxdWlyZSgnLi9kb2NzLWNvbnRlbnQudHN4Jyk7XG4gICAgY29uc3QgcGF0aCA9IHNsdWcgPyBzbHVnLmpvaW4oJy8nKSA6ICdpbmRleCc7XG5cbiAgICBjb25zdCBwYWdlRGF0YTogUmVjb3JkPHN0cmluZywgYW55PiA9IHtcbiAgICAgICdpbmRleCc6IHtcbiAgICAgICAgdGl0bGU6ICdXZWxjb21lIHRvIE9ubHlSdWxlcyBEb2N1bWVudGF0aW9uJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdMZWFybiBob3cgdG8gdXNlIE9ubHlSdWxlcyBBSSBQcm9tcHQgTWFuYWdlbWVudCBQbGF0Zm9ybScsXG4gICAgICAgIGJvZHk6IERvY3NDb250ZW50LmluZGV4LFxuICAgICAgfSxcbiAgICAgICdnZXR0aW5nLXN0YXJ0ZWQvcXVpY2stc3RhcnQnOiB7XG4gICAgICAgIHRpdGxlOiAnUXVpY2sgU3RhcnQgR3VpZGUnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0dldCBzdGFydGVkIHdpdGggT25seVJ1bGVzIGluIGp1c3QgYSBmZXcgbWludXRlcycsXG4gICAgICAgIGJvZHk6IERvY3NDb250ZW50WydnZXR0aW5nLXN0YXJ0ZWQvcXVpY2stc3RhcnQnXSxcbiAgICAgIH0sXG4gICAgICAnaWRlcy9jdXJzb3InOiB7XG4gICAgICAgIHRpdGxlOiAnQ3Vyc29yIElERSBJbnRlZ3JhdGlvbicsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGVhcm4gaG93IHRvIHVzZSBPbmx5UnVsZXMgd2l0aCBDdXJzb3IgSURFJyxcbiAgICAgICAgYm9keTogRG9jc0NvbnRlbnRbJ2lkZXMvY3Vyc29yJ10sXG4gICAgICB9LFxuICAgICAgJ2FwaS9vdmVydmlldyc6IHtcbiAgICAgICAgdGl0bGU6ICdBUEkgT3ZlcnZpZXcnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0xlYXJuIGFib3V0IHRoZSBPbmx5UnVsZXMgQVBJIGVuZHBvaW50cyBhbmQgaG93IHRvIHVzZSB0aGVtJyxcbiAgICAgICAgYm9keTogRG9jc0NvbnRlbnRbJ2FwaS9vdmVydmlldyddLFxuICAgICAgfSxcbiAgICAgICdndWlkZXMvc2VvLXNldHVwJzoge1xuICAgICAgICB0aXRsZTogJ1NFTyBTZXR1cCBmb3IgT25seVJ1bGVzJyxcbiAgICAgICAgZGVzY3JpcHRpb246ICdMZWFybiBob3cgdG8gY29uZmlndXJlIFNFTyBlbmhhbmNlbWVudHMgZm9yIHRoZSBPbmx5UnVsZXMgTmV4dC5qcyBhcHBsaWNhdGlvbicsXG4gICAgICAgIGJvZHk6IERvY3NDb250ZW50WydndWlkZXMvc2VvLXNldHVwJ10sXG4gICAgICB9LFxuICAgICAgJ2d1aWRlcy9pMThuJzoge1xuICAgICAgICB0aXRsZTogJ0ludGVybmF0aW9uYWxpemF0aW9uIChpMThuKSBHdWlkZScsXG4gICAgICAgIGRlc2NyaXB0aW9uOiAnTGVhcm4gaG93IHRvIHVzZSBMaW5ndWkuanMgZm9yIGludGVybmF0aW9uYWxpemF0aW9uIGluIE9ubHlSdWxlcycsXG4gICAgICAgIGJvZHk6IERvY3NDb250ZW50WydndWlkZXMvaTE4biddLFxuICAgICAgfSxcbiAgICAgICdndWlkZXMvcmFkaXgtdWktdGhlbWUnOiB7XG4gICAgICAgIHRpdGxlOiAnUmFkaXggVUkgVGhlbWUgdjMgR3VpZGUnLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ0xlYXJuIGhvdyB0byB1c2UgUmFkaXggVUkgVGhlbWUgdjMgaW4gdGhlIE9ubHlSdWxlcyBwcm9qZWN0JyxcbiAgICAgICAgYm9keTogRG9jc0NvbnRlbnRbJ2d1aWRlcy9yYWRpeC11aS10aGVtZSddLFxuICAgICAgfSxcbiAgICB9O1xuXG4gICAgY29uc3QgcGFnZSA9IHBhZ2VEYXRhW3BhdGhdO1xuICAgIGlmICghcGFnZSkgcmV0dXJuIG51bGw7XG5cbiAgICByZXR1cm4ge1xuICAgICAgZGF0YToge1xuICAgICAgICB0aXRsZTogcGFnZS50aXRsZSxcbiAgICAgICAgZGVzY3JpcHRpb246IHBhZ2UuZGVzY3JpcHRpb24sXG4gICAgICAgIGJvZHk6IHBhZ2UuYm9keSxcbiAgICAgICAgdG9jOiBbXSxcbiAgICAgICAgZnVsbDogZmFsc2UsXG4gICAgICAgIHN0cnVjdHVyZWREYXRhOiB7XG4gICAgICAgICAgdGl0bGU6IHBhZ2UudGl0bGUsXG4gICAgICAgICAgZGVzY3JpcHRpb246IHBhZ2UuZGVzY3JpcHRpb24sXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9O1xuICB9LFxuICBnZW5lcmF0ZVBhcmFtczogKCkgPT4ge1xuICAgIHJldHVybiBbXG4gICAgICB7IHNsdWc6IFsnaW5kZXgnXSB9LFxuICAgICAgeyBzbHVnOiBbJ2dldHRpbmctc3RhcnRlZCcsICdxdWljay1zdGFydCddIH0sXG4gICAgICB7IHNsdWc6IFsnaWRlcycsICdjdXJzb3InXSB9LFxuICAgICAgeyBzbHVnOiBbJ2FwaScsICdvdmVydmlldyddIH0sXG4gICAgICB7IHNsdWc6IFsnZ3VpZGVzJywgJ3Nlby1zZXR1cCddIH0sXG4gICAgICB7IHNsdWc6IFsnZ3VpZGVzJywgJ2kxOG4nXSB9LFxuICAgICAgeyBzbHVnOiBbJ2d1aWRlcycsICdyYWRpeC11aS10aGVtZSddIH0sXG4gICAgXTtcbiAgfSxcbiAgZ2V0UGFnZXM6ICgpID0+IHtcbiAgICByZXR1cm4gW1xuICAgICAgeyBzbHVnOiBbJ2luZGV4J10gfSxcbiAgICAgIHsgc2x1ZzogWydnZXR0aW5nLXN0YXJ0ZWQnLCAncXVpY2stc3RhcnQnXSB9LFxuICAgICAgeyBzbHVnOiBbJ2lkZXMnLCAnY3Vyc29yJ10gfSxcbiAgICAgIHsgc2x1ZzogWydhcGknLCAnb3ZlcnZpZXcnXSB9LFxuICAgICAgeyBzbHVnOiBbJ2d1aWRlcycsICdzZW8tc2V0dXAnXSB9LFxuICAgICAgeyBzbHVnOiBbJ2d1aWRlcycsICdpMThuJ10gfSxcbiAgICAgIHsgc2x1ZzogWydndWlkZXMnLCAncmFkaXgtdWktdGhlbWUnXSB9LFxuICAgIF07XG4gIH1cbn07XG4iXSwibmFtZXMiOlsic291cmNlIiwicGFnZVRyZWUiLCJ0eXBlIiwibmFtZSIsInVybCIsImNoaWxkcmVuIiwiZ2V0UGFnZSIsInNsdWciLCJEb2NzQ29udGVudCIsInJlcXVpcmUiLCJwYXRoIiwiam9pbiIsInBhZ2VEYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImJvZHkiLCJpbmRleCIsInBhZ2UiLCJkYXRhIiwidG9jIiwiZnVsbCIsInN0cnVjdHVyZWREYXRhIiwiZ2VuZXJhdGVQYXJhbXMiLCJnZXRQYWdlcyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./lib/source.ts\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Fhide-if-empty.js%22%2C%22ids%22%3A%5B%22HideIfEmpty%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Flanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Froot-toggle.js%22%2C%22ids%22%3A%5B%22RootToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsearch-toggle.js%22%2C%22ids%22%3A%5B%22LargeSearchToggle%22%2C%22SearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsidebar.js%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarFolder%22%2C%22SidebarFolderLink%22%2C%22SidebarFolderTrigger%22%2C%22SidebarFolderContent%22%2C%22SidebarItem%22%2C%22SidebarViewport%22%2C%22SidebarPageTree%22%2C%22SidebarContentMobile%22%2C%22SidebarHeader%22%2C%22SidebarFooter%22%2C%22SidebarContent%22%2C%22SidebarCollapseTrigger%22%2C%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Flayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Ftree.js%22%2C%22ids%22%3A%5B%22TreeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs-client.js%22%2C%22ids%22%3A%5B%22SidebarTrigger%22%2C%22CollapsibleControl%22%2C%22Navbar%22%2C%22LayoutBody%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Flinks.js%22%2C%22ids%22%3A%5B%22BaseLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fprovider%2Findex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2F(docs)%2Fdocs.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fjotai-provider.tsx%22%2C%22ids%22%3A%5B%22JotaiProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Ftoaster-provider.tsx%22%2C%22ids%22%3A%5B%22ToasterProvider%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Fhide-if-empty.js%22%2C%22ids%22%3A%5B%22HideIfEmpty%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Flanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Froot-toggle.js%22%2C%22ids%22%3A%5B%22RootToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsearch-toggle.js%22%2C%22ids%22%3A%5B%22LargeSearchToggle%22%2C%22SearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsidebar.js%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarFolder%22%2C%22SidebarFolderLink%22%2C%22SidebarFolderTrigger%22%2C%22SidebarFolderContent%22%2C%22SidebarItem%22%2C%22SidebarViewport%22%2C%22SidebarPageTree%22%2C%22SidebarContentMobile%22%2C%22SidebarHeader%22%2C%22SidebarFooter%22%2C%22SidebarContent%22%2C%22SidebarCollapseTrigger%22%2C%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Flayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Ftree.js%22%2C%22ids%22%3A%5B%22TreeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs-client.js%22%2C%22ids%22%3A%5B%22SidebarTrigger%22%2C%22CollapsibleControl%22%2C%22Navbar%22%2C%22LayoutBody%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Flinks.js%22%2C%22ids%22%3A%5B%22BaseLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fprovider%2Findex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2F(docs)%2Fdocs.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fjotai-provider.tsx%22%2C%22ids%22%3A%5B%22JotaiProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Ftoaster-provider.tsx%22%2C%22ids%22%3A%5B%22ToasterProvider%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-core/dist/hide-if-empty.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/hide-if-empty.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-core/dist/link.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/language-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/root-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/search-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/sidebar.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/theme-toggle.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/contexts/layout.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/layout.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/contexts/tree.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/tree.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/layouts/docs-client.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/docs-client.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/layouts/links.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/links.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/provider/index.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/provider/index.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/jotai-provider.tsx */ \"(ssr)/./components/providers/jotai-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/query-provider.tsx */ \"(ssr)/./components/providers/query-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/providers/toaster-provider.tsx */ \"(ssr)/./components/providers/toaster-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZub2RlX21vZHVsZXMlMkZmdW1hZG9jcy1jb3JlJTJGZGlzdCUyRmhpZGUtaWYtZW1wdHkuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJIaWRlSWZFbXB0eSUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZub2RlX21vZHVsZXMlMkZmdW1hZG9jcy1jb3JlJTJGZGlzdCUyRmxpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmFuZyUyRmNvZGVzcGFjZSUyRm9ubHlydWxlcy13ZWJzaXRlJTJGcHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRmZ1bWFkb2NzLXVpJTJGZGlzdCUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZsYW5ndWFnZS10b2dnbGUuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJMYW5ndWFnZVRvZ2dsZSUyMiUyQyUyMkxhbmd1YWdlVG9nZ2xlVGV4dCUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZub2RlX21vZHVsZXMlMkZmdW1hZG9jcy11aSUyRmRpc3QlMkZjb21wb25lbnRzJTJGbGF5b3V0JTJGcm9vdC10b2dnbGUuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSb290VG9nZ2xlJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmFuZyUyRmNvZGVzcGFjZSUyRm9ubHlydWxlcy13ZWJzaXRlJTJGcHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRmZ1bWFkb2NzLXVpJTJGZGlzdCUyRmNvbXBvbmVudHMlMkZsYXlvdXQlMkZzZWFyY2gtdG9nZ2xlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTGFyZ2VTZWFyY2hUb2dnbGUlMjIlMkMlMjJTZWFyY2hUb2dnbGUlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYW5nJTJGY29kZXNwYWNlJTJGb25seXJ1bGVzLXdlYnNpdGUlMkZwcm9qZWN0JTJGbm9kZV9tb2R1bGVzJTJGZnVtYWRvY3MtdWklMkZkaXN0JTJGY29tcG9uZW50cyUyRmxheW91dCUyRnNpZGViYXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTJDJTIyU2lkZWJhckZvbGRlciUyMiUyQyUyMlNpZGViYXJGb2xkZXJMaW5rJTIyJTJDJTIyU2lkZWJhckZvbGRlclRyaWdnZXIlMjIlMkMlMjJTaWRlYmFyRm9sZGVyQ29udGVudCUyMiUyQyUyMlNpZGViYXJJdGVtJTIyJTJDJTIyU2lkZWJhclZpZXdwb3J0JTIyJTJDJTIyU2lkZWJhclBhZ2VUcmVlJTIyJTJDJTIyU2lkZWJhckNvbnRlbnRNb2JpbGUlMjIlMkMlMjJTaWRlYmFySGVhZGVyJTIyJTJDJTIyU2lkZWJhckZvb3RlciUyMiUyQyUyMlNpZGViYXJDb250ZW50JTIyJTJDJTIyU2lkZWJhckNvbGxhcHNlVHJpZ2dlciUyMiUyQyUyMlNpZGViYXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYW5nJTJGY29kZXNwYWNlJTJGb25seXJ1bGVzLXdlYnNpdGUlMkZwcm9qZWN0JTJGbm9kZV9tb2R1bGVzJTJGZnVtYWRvY3MtdWklMkZkaXN0JTJGY29tcG9uZW50cyUyRmxheW91dCUyRnRoZW1lLXRvZ2dsZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRoZW1lVG9nZ2xlJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmFuZyUyRmNvZGVzcGFjZSUyRm9ubHlydWxlcy13ZWJzaXRlJTJGcHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRmZ1bWFkb2NzLXVpJTJGZGlzdCUyRmNvbnRleHRzJTJGbGF5b3V0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyTmF2UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYW5nJTJGY29kZXNwYWNlJTJGb25seXJ1bGVzLXdlYnNpdGUlMkZwcm9qZWN0JTJGbm9kZV9tb2R1bGVzJTJGZnVtYWRvY3MtdWklMkZkaXN0JTJGY29udGV4dHMlMkZ0cmVlLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVHJlZUNvbnRleHRQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZub2RlX21vZHVsZXMlMkZmdW1hZG9jcy11aSUyRmRpc3QlMkZsYXlvdXRzJTJGZG9jcy1jbGllbnQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJTaWRlYmFyVHJpZ2dlciUyMiUyQyUyMkNvbGxhcHNpYmxlQ29udHJvbCUyMiUyQyUyMk5hdmJhciUyMiUyQyUyMkxheW91dEJvZHklMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYW5nJTJGY29kZXNwYWNlJTJGb25seXJ1bGVzLXdlYnNpdGUlMkZwcm9qZWN0JTJGbm9kZV9tb2R1bGVzJTJGZnVtYWRvY3MtdWklMkZkaXN0JTJGbGF5b3V0cyUyRmxpbmtzLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQmFzZUxpbmtJdGVtJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmFuZyUyRmNvZGVzcGFjZSUyRm9ubHlydWxlcy13ZWJzaXRlJTJGcHJvamVjdCUyRm5vZGVfbW9kdWxlcyUyRmZ1bWFkb2NzLXVpJTJGZGlzdCUyRnByb3ZpZGVyJTJGaW5kZXguanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJSb290UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYW5nJTJGY29kZXNwYWNlJTJGb25seXJ1bGVzLXdlYnNpdGUlMkZwcm9qZWN0JTJGcGFja2FnZXMlMkZ3ZWIlMkZhcHAlMkZnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZwYWNrYWdlcyUyRndlYiUyRmFwcCUyRihkb2NzKSUyRmRvY3MuY3NzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGcmFuZyUyRmNvZGVzcGFjZSUyRm9ubHlydWxlcy13ZWJzaXRlJTJGcHJvamVjdCUyRnBhY2thZ2VzJTJGd2ViJTJGY29tcG9uZW50cyUyRnByb3ZpZGVycyUyRmpvdGFpLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkpvdGFpUHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZyYW5nJTJGY29kZXNwYWNlJTJGb25seXJ1bGVzLXdlYnNpdGUlMkZwcm9qZWN0JTJGcGFja2FnZXMlMkZ3ZWIlMkZjb21wb25lbnRzJTJGcHJvdmlkZXJzJTJGcXVlcnktcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUXVlcnlQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZwYWNrYWdlcyUyRndlYiUyRmNvbXBvbmVudHMlMkZwcm92aWRlcnMlMkZ0b2FzdGVyLXByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXJQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd05BQXNLO0FBQ3RLO0FBQ0Esc01BQXlKO0FBQ3pKO0FBQ0EsNFBBQWdOO0FBQ2hOO0FBQ0Esb1BBQW1MO0FBQ25MO0FBQ0Esd1BBQTJNO0FBQzNNO0FBQ0EsNE9BQStJO0FBQy9JO0FBQ0Esc1BBQXFMO0FBQ3JMO0FBQ0Esd05BQXNLO0FBQ3RLO0FBQ0Esb05BQTRLO0FBQzVLO0FBQ0EsZ09BQXdOO0FBQ3hOO0FBQ0Esb05BQXFLO0FBQ3JLO0FBQ0Esc05BQXNLO0FBQ3RLO0FBQ0EsOExBQTRLO0FBQzVLO0FBQ0EsOExBQTRLO0FBQzVLO0FBQ0Esa01BQWdMIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJIaWRlSWZFbXB0eVwiXSAqLyBcIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvaGlkZS1pZi1lbXB0eS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy1jb3JlL2Rpc3QvbGluay5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiTGFuZ3VhZ2VUb2dnbGVcIixcIkxhbmd1YWdlVG9nZ2xlVGV4dFwiXSAqLyBcIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy11aS9kaXN0L2NvbXBvbmVudHMvbGF5b3V0L2xhbmd1YWdlLXRvZ2dsZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUm9vdFRvZ2dsZVwiXSAqLyBcIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L25vZGVfbW9kdWxlcy9mdW1hZG9jcy11aS9kaXN0L2NvbXBvbmVudHMvbGF5b3V0L3Jvb3QtdG9nZ2xlLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJMYXJnZVNlYXJjaFRvZ2dsZVwiLFwiU2VhcmNoVG9nZ2xlXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvY29tcG9uZW50cy9sYXlvdXQvc2VhcmNoLXRvZ2dsZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvY29tcG9uZW50cy9sYXlvdXQvc2lkZWJhci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVGhlbWVUb2dnbGVcIl0gKi8gXCIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9ub2RlX21vZHVsZXMvZnVtYWRvY3MtdWkvZGlzdC9jb21wb25lbnRzL2xheW91dC90aGVtZS10b2dnbGUuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIk5hdlByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvY29udGV4dHMvbGF5b3V0LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJUcmVlQ29udGV4dFByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvY29udGV4dHMvdHJlZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiU2lkZWJhclRyaWdnZXJcIixcIkNvbGxhcHNpYmxlQ29udHJvbFwiLFwiTmF2YmFyXCIsXCJMYXlvdXRCb2R5XCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvbGF5b3V0cy9kb2NzLWNsaWVudC5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiQmFzZUxpbmtJdGVtXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvbGF5b3V0cy9saW5rcy5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiUm9vdFByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3Qvbm9kZV9tb2R1bGVzL2Z1bWFkb2NzLXVpL2Rpc3QvcHJvdmlkZXIvaW5kZXguanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkpvdGFpUHJvdmlkZXJcIl0gKi8gXCIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9wYWNrYWdlcy93ZWIvY29tcG9uZW50cy9wcm92aWRlcnMvam90YWktcHJvdmlkZXIudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJRdWVyeVByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL2NvbXBvbmVudHMvcHJvdmlkZXJzL3F1ZXJ5LXByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVG9hc3RlclByb3ZpZGVyXCJdICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL2NvbXBvbmVudHMvcHJvdmlkZXJzL3RvYXN0ZXItcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Fhide-if-empty.js%22%2C%22ids%22%3A%5B%22HideIfEmpty%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Flanguage-toggle.js%22%2C%22ids%22%3A%5B%22LanguageToggle%22%2C%22LanguageToggleText%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Froot-toggle.js%22%2C%22ids%22%3A%5B%22RootToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsearch-toggle.js%22%2C%22ids%22%3A%5B%22LargeSearchToggle%22%2C%22SearchToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Fsidebar.js%22%2C%22ids%22%3A%5B%22*%22%2C%22SidebarFolder%22%2C%22SidebarFolderLink%22%2C%22SidebarFolderTrigger%22%2C%22SidebarFolderContent%22%2C%22SidebarItem%22%2C%22SidebarViewport%22%2C%22SidebarPageTree%22%2C%22SidebarContentMobile%22%2C%22SidebarHeader%22%2C%22SidebarFooter%22%2C%22SidebarContent%22%2C%22SidebarCollapseTrigger%22%2C%22Sidebar%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftheme-toggle.js%22%2C%22ids%22%3A%5B%22ThemeToggle%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Flayout.js%22%2C%22ids%22%3A%5B%22NavProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Ftree.js%22%2C%22ids%22%3A%5B%22TreeContextProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs-client.js%22%2C%22ids%22%3A%5B%22SidebarTrigger%22%2C%22CollapsibleControl%22%2C%22Navbar%22%2C%22LayoutBody%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Flinks.js%22%2C%22ids%22%3A%5B%22BaseLinkItem%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fprovider%2Findex.js%22%2C%22ids%22%3A%5B%22RootProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2F(docs)%2Fdocs.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fjotai-provider.tsx%22%2C%22ids%22%3A%5B%22JotaiProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Fquery-provider.tsx%22%2C%22ids%22%3A%5B%22QueryProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fcomponents%2Fproviders%2Ftoaster-provider.tsx%22%2C%22ids%22%3A%5B%22ToasterProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc-clerk.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc.js%22%2C%22ids%22%3A%5B%22TOCScrollArea%22%2C%22TOCItems%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Fi18n.js%22%2C%22ids%22%3A%5B%22*%22%2C%22I18nLabel%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs%2Fpage-client.js%22%2C%22ids%22%3A%5B%22PageRoot%22%2C%22PageBreadcrumb%22%2C%22PageFooter%22%2C%22PageLastUpdate%22%2C%22PageTOC%22%2C%22PageTOCPopover%22%2C%22PageTOCPopoverTrigger%22%2C%22PageTOCPopoverContent%22%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc-clerk.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc.js%22%2C%22ids%22%3A%5B%22TOCScrollArea%22%2C%22TOCItems%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Fi18n.js%22%2C%22ids%22%3A%5B%22*%22%2C%22I18nLabel%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs%2Fpage-client.js%22%2C%22ids%22%3A%5B%22PageRoot%22%2C%22PageBreadcrumb%22%2C%22PageFooter%22%2C%22PageLastUpdate%22%2C%22PageTOC%22%2C%22PageTOCPopover%22%2C%22PageTOCPopoverTrigger%22%2C%22PageTOCPopoverContent%22%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-core/dist/link.js */ \"(ssr)/../../node_modules/fumadocs-core/dist/link.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/toc-clerk.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/components/layout/toc.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/components/layout/toc.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/contexts/i18n.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/contexts/i18n.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js */ \"(ssr)/../../node_modules/fumadocs-ui/dist/layouts/docs/page-client.js\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-core%2Fdist%2Flink.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc-clerk.js%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcomponents%2Flayout%2Ftoc.js%22%2C%22ids%22%3A%5B%22TOCScrollArea%22%2C%22TOCItems%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Fcontexts%2Fi18n.js%22%2C%22ids%22%3A%5B%22*%22%2C%22I18nLabel%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Ffumadocs-ui%2Fdist%2Flayouts%2Fdocs%2Fpage-client.js%22%2C%22ids%22%3A%5B%22PageRoot%22%2C%22PageBreadcrumb%22%2C%22PageFooter%22%2C%22PageLastUpdate%22%2C%22PageTOC%22%2C%22PageTOCPopover%22%2C%22PageTOCPopoverTrigger%22%2C%22PageTOCPopoverContent%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/../../node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/../../node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/../../node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ../../node_modules/next/dist/lib/metadata/generate/icon-mark.js */ \"(ssr)/../../node_modules/next/dist/lib/metadata/generate/icon-mark.js\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js */ \"(ssr)/../../node_modules/next/dist/next-devtools/userspace/app/segment-explorer-node.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Flib%2Fmetadata%2Fgenerate%2Ficon-mark.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fnode_modules%2Fnext%2Fdist%2Fnext-devtools%2Fuserspace%2Fapp%2Fsegment-explorer-node.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/global-error.tsx */ \"(ssr)/./app/global-error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9idWlsZC93ZWJwYWNrL2xvYWRlcnMvbmV4dC1mbGlnaHQtY2xpZW50LWVudHJ5LWxvYWRlci5qcz9tb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRnJhbmclMkZjb2Rlc3BhY2UlMkZvbmx5cnVsZXMtd2Vic2l0ZSUyRnByb2plY3QlMkZwYWNrYWdlcyUyRndlYiUyRmFwcCUyRmdsb2JhbC1lcnJvci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdKQUFzSCIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL2FwcC9nbG9iYWwtZXJyb3IudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp%2Fglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/global-error.tsx":
/*!******************************!*\
  !*** ./app/global-error.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalError)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/themes */ \"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/container.js\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/themes */ \"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/flex.js\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/themes */ \"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/heading.js\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/themes */ \"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/text.js\");\n/* harmony import */ var _radix_ui_themes__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/themes */ \"(ssr)/../../node_modules/@radix-ui/themes/dist/esm/components/button.js\");\n/* harmony import */ var _barrel_optimize_names_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Home,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Home,RefreshCw!=!lucide-react */ \"(ssr)/../../node_modules/lucide-react/dist/esm/icons/house.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction GlobalError({ error, reset }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"GlobalError.useEffect\": ()=>{\n            // Log the error to an error reporting service\n            console.error('Global error:', error);\n        }\n    }[\"GlobalError.useEffect\"], [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_2__.Container, {\n                size: \"2\",\n                className: \"min-h-screen flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__.Flex, {\n                    direction: \"column\",\n                    align: \"center\",\n                    gap: \"6\",\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_4__.Heading, {\n                            size: \"8\",\n                            className: \"text-5xl font-bold text-red-600\",\n                            children: \"Something went wrong!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            size: \"4\",\n                            color: \"gray\",\n                            className: \"max-w-md\",\n                            children: \"We're sorry, but something unexpected happened. Please try refreshing the page or go back to the homepage.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 13\n                        }, this),\n                        error.digest && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_5__.Text, {\n                            size: \"2\",\n                            color: \"gray\",\n                            className: \"font-mono\",\n                            children: [\n                                \"Error ID: \",\n                                error.digest\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_3__.Flex, {\n                            gap: \"3\",\n                            className: \"mt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"solid\",\n                                    onClick: reset,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Try again\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                                    lineNumber: 36,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_themes__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                    variant: \"outline\",\n                                    asChild: true,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Home_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                                                lineNumber: 42,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Go Home\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                                        lineNumber: 41,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n                lineNumber: 22,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/app/global-error.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/global-error.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/jotai-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/jotai-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JotaiProvider: () => (/* binding */ JotaiProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai */ \"(ssr)/../../node_modules/jotai/esm/react.mjs\");\n/* __next_internal_client_entry_do_not_use__ JotaiProvider auto */ \n\nfunction JotaiProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(jotai__WEBPACK_IMPORTED_MODULE_1__.Provider, {\n        children: children\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/jotai-provider.tsx\",\n        lineNumber: 6,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy9qb3RhaS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFFaUM7QUFFMUIsU0FBU0MsY0FBYyxFQUFFQyxRQUFRLEVBQWlDO0lBQ3ZFLHFCQUFPLDhEQUFDRiwyQ0FBUUE7a0JBQUVFOzs7Ozs7QUFDcEIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9jb21wb25lbnRzL3Byb3ZpZGVycy9qb3RhaS1wcm92aWRlci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5cbmltcG9ydCB7IFByb3ZpZGVyIH0gZnJvbSAnam90YWknO1xuXG5leHBvcnQgZnVuY3Rpb24gSm90YWlQcm92aWRlcih7IGNoaWxkcmVuIH06IHsgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZSB9KSB7XG4gIHJldHVybiA8UHJvdmlkZXI+e2NoaWxkcmVufTwvUHJvdmlkZXI+O1xufSJdLCJuYW1lcyI6WyJQcm92aWRlciIsIkpvdGFpUHJvdmlkZXIiLCJjaGlsZHJlbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/jotai-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/query-provider.tsx":
/*!*************************************************!*\
  !*** ./components/providers/query-provider.tsx ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryProvider: () => (/* binding */ QueryProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../../node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var _lib_query_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/query-client */ \"(ssr)/./lib/query-client.ts\");\n/* __next_internal_client_entry_do_not_use__ QueryProvider auto */ \n\n\n\nfunction QueryProvider({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_2__.QueryClientProvider, {\n        client: _lib_query_client__WEBPACK_IMPORTED_MODULE_1__.queryClient,\n        children: [\n            children,\n             true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_3__.ReactQueryDevtools, {\n                initialIsOpen: false,\n                position: \"bottom-right\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/query-provider.tsx\",\n                lineNumber: 17,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/query-provider.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy9xdWVyeS1wcm92aWRlci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUU0RDtBQUNRO0FBQ25CO0FBTTFDLFNBQVNHLGNBQWMsRUFBRUMsUUFBUSxFQUFzQjtJQUM1RCxxQkFDRSw4REFBQ0osc0VBQW1CQTtRQUFDSyxRQUFRSCwwREFBV0E7O1lBQ3JDRTtpQkFFc0Msa0JBQ3JDLDhEQUFDSCw4RUFBa0JBO2dCQUNqQkssZUFBZTtnQkFDZkMsVUFBUzs7Ozs7Ozs7Ozs7O0FBS25CIiwic291cmNlcyI6WyIvVXNlcnMvcmFuZy9jb2Rlc3BhY2Uvb25seXJ1bGVzLXdlYnNpdGUvcHJvamVjdC9wYWNrYWdlcy93ZWIvY29tcG9uZW50cy9wcm92aWRlcnMvcXVlcnktcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgeyBRdWVyeUNsaWVudFByb3ZpZGVyIH0gZnJvbSAnQHRhbnN0YWNrL3JlYWN0LXF1ZXJ5JztcbmltcG9ydCB7IFJlYWN0UXVlcnlEZXZ0b29scyB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeS1kZXZ0b29scyc7XG5pbXBvcnQgeyBxdWVyeUNsaWVudCB9IGZyb20gJ0AvbGliL3F1ZXJ5LWNsaWVudCc7XG5cbmludGVyZmFjZSBRdWVyeVByb3ZpZGVyUHJvcHMge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gUXVlcnlQcm92aWRlcih7IGNoaWxkcmVuIH06IFF1ZXJ5UHJvdmlkZXJQcm9wcykge1xuICByZXR1cm4gKFxuICAgIDxRdWVyeUNsaWVudFByb3ZpZGVyIGNsaWVudD17cXVlcnlDbGllbnR9PlxuICAgICAge2NoaWxkcmVufVxuICAgICAgey8qIE9ubHkgc2hvdyBkZXZ0b29scyBpbiBkZXZlbG9wbWVudCAqL31cbiAgICAgIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAoXG4gICAgICAgIDxSZWFjdFF1ZXJ5RGV2dG9vbHMgXG4gICAgICAgICAgaW5pdGlhbElzT3Blbj17ZmFsc2V9XG4gICAgICAgICAgcG9zaXRpb249XCJib3R0b20tcmlnaHRcIlxuICAgICAgICAvPlxuICAgICAgKX1cbiAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XG4gICk7XG59Il0sIm5hbWVzIjpbIlF1ZXJ5Q2xpZW50UHJvdmlkZXIiLCJSZWFjdFF1ZXJ5RGV2dG9vbHMiLCJxdWVyeUNsaWVudCIsIlF1ZXJ5UHJvdmlkZXIiLCJjaGlsZHJlbiIsImNsaWVudCIsImluaXRpYWxJc09wZW4iLCJwb3NpdGlvbiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/query-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/providers/toaster-provider.tsx":
/*!***************************************************!*\
  !*** ./components/providers/toaster-provider.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ToasterProvider: () => (/* binding */ ToasterProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sonner */ \"(ssr)/./components/ui/sonner.tsx\");\n/* __next_internal_client_entry_do_not_use__ ToasterProvider auto */ \n\n\nfunction ToasterProvider() {\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ToasterProvider.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"ToasterProvider.useEffect\"], []);\n    // Only render after hydration to prevent SSR issues\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/providers/toaster-provider.tsx\",\n        lineNumber: 18,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3Byb3ZpZGVycy90b2FzdGVyLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRTRDO0FBQ0s7QUFFMUMsU0FBU0c7SUFDZCxNQUFNLENBQUNDLFNBQVNDLFdBQVcsR0FBR0osK0NBQVFBLENBQUM7SUFFdkNELGdEQUFTQTtxQ0FBQztZQUNSSyxXQUFXO1FBQ2I7b0NBQUcsRUFBRTtJQUVMLG9EQUFvRDtJQUNwRCxJQUFJLENBQUNELFNBQVM7UUFDWixPQUFPO0lBQ1Q7SUFFQSxxQkFBTyw4REFBQ0YsMERBQU9BOzs7OztBQUNqQiIsInNvdXJjZXMiOlsiL1VzZXJzL3JhbmcvY29kZXNwYWNlL29ubHlydWxlcy13ZWJzaXRlL3Byb2plY3QvcGFja2FnZXMvd2ViL2NvbXBvbmVudHMvcHJvdmlkZXJzL3RvYXN0ZXItcHJvdmlkZXIudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IFRvYXN0ZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvc29ubmVyJztcblxuZXhwb3J0IGZ1bmN0aW9uIFRvYXN0ZXJQcm92aWRlcigpIHtcbiAgY29uc3QgW21vdW50ZWQsIHNldE1vdW50ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgc2V0TW91bnRlZCh0cnVlKTtcbiAgfSwgW10pO1xuXG4gIC8vIE9ubHkgcmVuZGVyIGFmdGVyIGh5ZHJhdGlvbiB0byBwcmV2ZW50IFNTUiBpc3N1ZXNcbiAgaWYgKCFtb3VudGVkKSB7XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cblxuICByZXR1cm4gPFRvYXN0ZXIgLz47XG59XG4iXSwibmFtZXMiOlsidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJUb2FzdGVyIiwiVG9hc3RlclByb3ZpZGVyIiwibW91bnRlZCIsInNldE1vdW50ZWQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/providers/toaster-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/sonner.tsx":
/*!**********************************!*\
  !*** ./components/ui/sonner.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/../../node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/../../node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = 'system' } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"Toaster.useEffect\": ()=>{\n            setMounted(true);\n        }\n    }[\"Toaster.useEffect\"], []);\n    // Don't render during SSR to avoid hydration issues\n    if (!mounted) {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: 'group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg',\n                description: 'group-[.toast]:text-muted-foreground',\n                actionButton: 'group-[.toast]:bg-primary group-[.toast]:text-primary-foreground',\n                cancelButton: 'group-[.toast]:bg-muted group-[.toast]:text-muted-foreground'\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/codespace/onlyrules-website/project/packages/web/components/ui/sonner.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFdUM7QUFDSTtBQUNDO0FBSTVDLE1BQU1DLFVBQVUsQ0FBQyxFQUFFLEdBQUdJLE9BQXFCO0lBQ3pDLE1BQU0sRUFBRUMsUUFBUSxRQUFRLEVBQUUsR0FBR04scURBQVFBO0lBQ3JDLE1BQU0sQ0FBQ08sU0FBU0MsV0FBVyxHQUFHSiwrQ0FBUUEsQ0FBQztJQUV2Q0QsZ0RBQVNBOzZCQUFDO1lBQ1JLLFdBQVc7UUFDYjs0QkFBRyxFQUFFO0lBRUwsb0RBQW9EO0lBQ3BELElBQUksQ0FBQ0QsU0FBUztRQUNaLE9BQU87SUFDVDtJQUVBLHFCQUNFLDhEQUFDTCwyQ0FBTUE7UUFDTEksT0FBT0E7UUFDUEcsV0FBVTtRQUNWQyxjQUFjO1lBQ1pDLFlBQVk7Z0JBQ1ZDLE9BQ0U7Z0JBQ0ZDLGFBQWE7Z0JBQ2JDLGNBQ0U7Z0JBQ0ZDLGNBQ0U7WUFDSjtRQUNGO1FBQ0MsR0FBR1YsS0FBSzs7Ozs7O0FBR2Y7QUFFbUIiLCJzb3VyY2VzIjpbIi9Vc2Vycy9yYW5nL2NvZGVzcGFjZS9vbmx5cnVsZXMtd2Vic2l0ZS9wcm9qZWN0L3BhY2thZ2VzL3dlYi9jb21wb25lbnRzL3VpL3Nvbm5lci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VUaGVtZSB9IGZyb20gJ25leHQtdGhlbWVzJztcbmltcG9ydCB7IFRvYXN0ZXIgYXMgU29ubmVyIH0gZnJvbSAnc29ubmVyJztcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCc7XG5cbnR5cGUgVG9hc3RlclByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHM8dHlwZW9mIFNvbm5lcj47XG5cbmNvbnN0IFRvYXN0ZXIgPSAoeyAuLi5wcm9wcyB9OiBUb2FzdGVyUHJvcHMpID0+IHtcbiAgY29uc3QgeyB0aGVtZSA9ICdzeXN0ZW0nIH0gPSB1c2VUaGVtZSgpO1xuICBjb25zdCBbbW91bnRlZCwgc2V0TW91bnRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZXRNb3VudGVkKHRydWUpO1xuICB9LCBbXSk7XG5cbiAgLy8gRG9uJ3QgcmVuZGVyIGR1cmluZyBTU1IgdG8gYXZvaWQgaHlkcmF0aW9uIGlzc3Vlc1xuICBpZiAoIW1vdW50ZWQpIHtcbiAgICByZXR1cm4gbnVsbDtcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPFNvbm5lclxuICAgICAgdGhlbWU9e3RoZW1lIGFzIFRvYXN0ZXJQcm9wc1sndGhlbWUnXX1cbiAgICAgIGNsYXNzTmFtZT1cInRvYXN0ZXIgZ3JvdXBcIlxuICAgICAgdG9hc3RPcHRpb25zPXt7XG4gICAgICAgIGNsYXNzTmFtZXM6IHtcbiAgICAgICAgICB0b2FzdDpcbiAgICAgICAgICAgICdncm91cCB0b2FzdCBncm91cC1bLnRvYXN0ZXJdOmJnLWJhY2tncm91bmQgZ3JvdXAtWy50b2FzdGVyXTp0ZXh0LWZvcmVncm91bmQgZ3JvdXAtWy50b2FzdGVyXTpib3JkZXItYm9yZGVyIGdyb3VwLVsudG9hc3Rlcl06c2hhZG93LWxnJyxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ2dyb3VwLVsudG9hc3RdOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCcsXG4gICAgICAgICAgYWN0aW9uQnV0dG9uOlxuICAgICAgICAgICAgJ2dyb3VwLVsudG9hc3RdOmJnLXByaW1hcnkgZ3JvdXAtWy50b2FzdF06dGV4dC1wcmltYXJ5LWZvcmVncm91bmQnLFxuICAgICAgICAgIGNhbmNlbEJ1dHRvbjpcbiAgICAgICAgICAgICdncm91cC1bLnRvYXN0XTpiZy1tdXRlZCBncm91cC1bLnRvYXN0XTp0ZXh0LW11dGVkLWZvcmVncm91bmQnLFxuICAgICAgICB9LFxuICAgICAgfX1cbiAgICAgIHsuLi5wcm9wc31cbiAgICAvPlxuICApO1xufTtcblxuZXhwb3J0IHsgVG9hc3RlciB9O1xuIl0sIm5hbWVzIjpbInVzZVRoZW1lIiwiVG9hc3RlciIsIlNvbm5lciIsInVzZUVmZmVjdCIsInVzZVN0YXRlIiwicHJvcHMiLCJ0aGVtZSIsIm1vdW50ZWQiLCJzZXRNb3VudGVkIiwiY2xhc3NOYW1lIiwidG9hc3RPcHRpb25zIiwiY2xhc3NOYW1lcyIsInRvYXN0IiwiZGVzY3JpcHRpb24iLCJhY3Rpb25CdXR0b24iLCJjYW5jZWxCdXR0b24iXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/query-client.ts":
/*!*****************************!*\
  !*** ./lib/query-client.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   queryClient: () => (/* binding */ queryClient),\n/* harmony export */   queryKeys: () => (/* binding */ queryKeys)\n/* harmony export */ });\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../../node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(ssr)/../../node_modules/sonner/dist/index.mjs\");\n\n\n// Create a query client with default options\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_1__.QueryClient({\n    defaultOptions: {\n        queries: {\n            // How long data is considered fresh (5 minutes)\n            staleTime: 5 * 60 * 1000,\n            // How long data is cached (10 minutes)\n            gcTime: 10 * 60 * 1000,\n            // Retry failed requests\n            retry: (failureCount, error)=>{\n                // Don't retry on 4xx errors (client errors)\n                if (error?.status >= 400 && error?.status < 500) {\n                    return false;\n                }\n                // Retry up to 3 times for server errors\n                return failureCount < 3;\n            },\n            // Refetch on window focus (useful for real-time data)\n            refetchOnWindowFocus: false,\n            // Error handling\n            throwOnError: false\n        },\n        mutations: {\n            retry: false,\n            // Global error handler for mutations\n            onError: (error)=>{\n                console.error('Mutation error:', error);\n                const message = error?.message || 'An error occurred';\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(message);\n            }\n        }\n    }\n});\n// Query keys factory for consistent key management\nconst queryKeys = {\n    // Rules\n    rules: {\n        all: [\n            'rules'\n        ],\n        lists: ()=>[\n                ...queryKeys.rules.all,\n                'list'\n            ],\n        list: (filters)=>[\n                ...queryKeys.rules.lists(),\n                filters\n            ],\n        details: ()=>[\n                ...queryKeys.rules.all,\n                'detail'\n            ],\n        detail: (id)=>[\n                ...queryKeys.rules.details(),\n                id\n            ],\n        raw: (id)=>[\n                ...queryKeys.rules.all,\n                'raw',\n                id\n            ]\n    },\n    // Rulesets\n    rulesets: {\n        all: [\n            'rulesets'\n        ],\n        lists: ()=>[\n                ...queryKeys.rulesets.all,\n                'list'\n            ],\n        list: (filters)=>[\n                ...queryKeys.rulesets.lists(),\n                filters\n            ],\n        details: ()=>[\n                ...queryKeys.rulesets.all,\n                'detail'\n            ],\n        detail: (id)=>[\n                ...queryKeys.rulesets.details(),\n                id\n            ]\n    },\n    // Tags\n    tags: {\n        all: [\n            'tags'\n        ],\n        lists: ()=>[\n                ...queryKeys.tags.all,\n                'list'\n            ]\n    },\n    // User data\n    user: {\n        all: [\n            'user'\n        ],\n        profile: (username)=>[\n                ...queryKeys.user.all,\n                'profile',\n                username\n            ]\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/query-client.ts\n");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/dynamic-access-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/server/app-render/dynamic-access-async-storage.external.js" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/dynamic-access-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/is-bot":
/*!***********************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/is-bot" ***!
  \***********************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/is-bot");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:process":
/*!*******************************!*\
  !*** external "node:process" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:process");

/***/ }),

/***/ "node:url":
/*!***************************!*\
  !*** external "node:url" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:url");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/fumadocs-core","vendor-chunks/@tanstack","vendor-chunks/fumadocs-ui","vendor-chunks/@radix-ui","vendor-chunks/github-slugger","vendor-chunks/next","vendor-chunks/mdast-util-to-markdown","vendor-chunks/micromark-core-commonmark","vendor-chunks/micromark","vendor-chunks/react-remove-scroll","vendor-chunks/lucide-react","vendor-chunks/@floating-ui","vendor-chunks/micromark-util-symbol","vendor-chunks/@swc","vendor-chunks/debug","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/jotai","vendor-chunks/use-callback-ref","vendor-chunks/vfile","vendor-chunks/unist-util-visit-parents","vendor-chunks/unified","vendor-chunks/micromark-util-subtokenize","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/use-sidecar","vendor-chunks/zwitch","vendor-chunks/vfile-message","vendor-chunks/unist-util-visit","vendor-chunks/unist-util-stringify-position","vendor-chunks/unist-util-is","vendor-chunks/trough","vendor-chunks/remark","vendor-chunks/remark-stringify","vendor-chunks/remark-parse","vendor-chunks/micromark-util-resolve-all","vendor-chunks/micromark-util-normalize-identifier","vendor-chunks/micromark-util-html-tag-name","vendor-chunks/micromark-util-decode-string","vendor-chunks/micromark-util-decode-numeric-character-reference","vendor-chunks/micromark-util-combine-extensions","vendor-chunks/micromark-util-classify-character","vendor-chunks/micromark-util-chunked","vendor-chunks/micromark-util-character","vendor-chunks/micromark-factory-whitespace","vendor-chunks/micromark-factory-title","vendor-chunks/micromark-factory-space","vendor-chunks/micromark-factory-label","vendor-chunks/micromark-factory-destination","vendor-chunks/mdast-util-to-string","vendor-chunks/mdast-util-phrasing","vendor-chunks/mdast-util-from-markdown","vendor-chunks/longest-streak","vendor-chunks/is-plain-obj","vendor-chunks/devlop","vendor-chunks/dequal","vendor-chunks/decode-named-character-reference","vendor-chunks/character-entities","vendor-chunks/bail","vendor-chunks/tslib","vendor-chunks/sonner","vendor-chunks/scroll-into-view-if-needed","vendor-chunks/next-themes","vendor-chunks/compute-scroll-into-view","vendor-chunks/classnames","vendor-chunks/supports-color","vendor-chunks/ms","vendor-chunks/has-flag","vendor-chunks/extend","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/../../node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&page=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&appPaths=%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage&pagePath=private-next-app-dir%2F(docs)%2Fdocs%2F%5B%5B...slug%5D%5D%2Fpage.tsx&appDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Frang%2Fcodespace%2Fonlyrules-website%2Fproject%2Fpackages%2Fweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();